import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild, inject, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { CardModule } from 'primeng/card';
import { Menu, MenuModule } from 'primeng/menu';
import { ButtonModule } from 'primeng/button';
import { RadioButtonModule } from 'primeng/radiobutton';
import { FormsModule } from '@angular/forms';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmationService } from 'primeng/api';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ProductDetail, SecuredMenuItem } from 'src/app/models/interface/pms';
import { LIFECYCLE_STAGE_DOC_MAP, Status, STATUS_MAP } from 'src/app/models/constant/pms';
import { Router } from '@angular/router';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { ProductionInstruction } from 'src/app/models/interface/ptm';

interface Row {
    prototype: any;
    pilot?: any;
    mp?: any;
}

@Component({
    selector: 'app-production-instruction-version',
    standalone: true,
    imports: [CommonModule, TableModule, CardModule, MenuModule, ButtonModule, RadioButtonModule, FormsModule, CheckboxModule, HasAnyAuthorityDirective],
    templateUrl: './production-instruction-version.component.html',
    styleUrls: ['./production-instruction-version.component.scss'],
    providers: [ProductFileService],
})
export class ProductionInstructionVersionComponent implements OnInit, OnChanges {
    @Input() currentProduct: ProductDetail | null;
    @Input() productionInstructions: ProductionInstruction[] | [];
    @Output() openTransferPopup = new EventEmitter<any>();
    @Output() openApprovalPopup = new EventEmitter<any>();
    @Output() openLifecycleStagePopup = new EventEmitter<{ version: any; type: string }>();
    @Output() openSendApprovalPopup = new EventEmitter<any>();
    @Output() openHistoryChangedPopup = new EventEmitter<any>();
    @Output() openClonePopup = new EventEmitter<any>();
    @Output() loadVersionProfiles = new EventEmitter<any>();

    @ViewChild('menu') menu!: Menu;
    productFileService = inject(ProductFileService);

    columns = ['Prototype', 'Pilot', 'MP'];
    versionKeys: Array<'prototype' | 'pilot' | 'mp'> = ['prototype', 'pilot', 'mp'];
    tableRows: Row[] = [];
    menuItems: SecuredMenuItem[] = [];
    currentVersion!: any;
    constructor(
        private router: Router,
        private confirmationService: ConfirmationService,
        private alertService: AlertService,
    ) {}

    ngOnInit(): void {}
    ngOnChanges(changes: SimpleChanges) {
        if (changes['productionInstructions'] && changes['productionInstructions'].currentValue) {
            const stageKeys = Object.keys(LIFECYCLE_STAGE_DOC_MAP).map((k) => +k);
            const [prototypeStage, pilotStage, mpStage] = stageKeys;
            const prototypeVersions = this.productionInstructions.filter((v: ProductionInstruction) => v.phase === prototypeStage).map(this.mapToVersion);

            const pilotVersions = this.productionInstructions.filter((v: ProductionInstruction) => v.phase === pilotStage).map(this.mapToVersion);

            const mpVersions = this.productionInstructions.filter((v: ProductionInstruction) => v.phase === mpStage).map(this.mapToVersion);

            // 1. Gom tất cả version vào một Set
            const allVersions = new Set<string>();
            prototypeVersions.forEach((p) => allVersions.add(p.version));
            pilotVersions.forEach((p) => allVersions.add(p.version));
            mpVersions.forEach((m) => allVersions.add(m.version));

            // 2. Chuyển Set thành mảng và build tableRows
            this.tableRows = Array.from(allVersions).map((version) => {
                const proto = prototypeVersions.find((p) => p.version === version);
                const pilot = pilotVersions.find((v) => v.version === version);
                const mp = mpVersions.find((v) => v.version === version);

                return {
                    prototype: proto ? { ...proto, selected: false } : undefined,
                    pilot: pilot ? { ...pilot, selected: false } : undefined,
                    mp: mp ? { ...mp, selected: false } : undefined,
                };
            });

            this.tableRows = this.sortRows(this.tableRows);
        }
    }
    getVersionValue(obj) {
        if (obj.prototype && obj.prototype.version) {
            return parseFloat(obj.prototype.version);
        }
        if (obj.pilot && obj.pilot.version) {
            return parseFloat(obj.pilot.version);
        }
        if (obj.mp && obj.mp.version) {
            return parseFloat(obj.mp.version);
        }
        return Infinity;
    }

    hasCurrent(obj) {
        return (
            (obj.prototype != null && obj.prototype.isCurrent === 1) ||
            (obj.pilot != null && obj.pilot.isCurrent === 1) ||
            (obj.mp != null && obj.mp.isCurrent === 1)
        );
    }

    sortRows(arr) {
        return arr.slice().sort((a, b) => {
            const aIsCurr = this.hasCurrent(a) ? 0 : 1;
            const bIsCurr = this.hasCurrent(b) ? 0 : 1;
            if (aIsCurr !== bIsCurr) {
                return aIsCurr - bIsCurr; // các đối tượng có isCurrent===1 sẽ về đầu
            }
            return this.getVersionValue(a) - this.getVersionValue(b);
        });
    }

    //    Hàm mapper DTO → UI-model
    mapToVersion(v: any): any {
        return {
            id: v.id,
            versionId: v.versionId,
            version: v.versionName,
            lastUpdate: new Date(v.updated),
            statusName: STATUS_MAP[v.status] ?? 'Draft',
            status: v.status,
            selected: false,
            note: v.note ?? '',
            lifecycleStage: v.phase ?? 0,
            isCurrent: v.isCurrent,
            created: new Date(v.created),
            approvalTime: v.approvalTime ? new Date(v.approvalTime) : null,
            compared: v.compared,
            compareUrl: v.compareUrl,
        };
    }
    onMenuClick(event: MouseEvent, productionInstruction: any, phase: 'prototype' | 'pilot' | 'mp') {
        // this.currentVersion = version;

        this.menuItems = this.getMenuItems(productionInstruction, phase);
        this.menu.toggle(event);
    }
    getMenuItems(productionInstruction: any, phase: 'prototype' | 'pilot' | 'mp'): SecuredMenuItem[] {
        const allItems: SecuredMenuItem[] = [
            {
                label: 'Xem chi tiết',
                icon: 'pi pi-eye', // Icon xem chi tiết
                command: () => this.viewDetail(productionInstruction),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_detail_view'],
            },
            {
                label: 'Chỉnh sửa',
                icon: 'pi pi-pencil',
                command: () => this.editVersion(productionInstruction),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_update'],
            },
            {
                label: 'Nhân bản',
                icon: 'pi pi-clone', // Icon nhân bản
                command: () => this.cloneVersion(productionInstruction),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_create'],
            },
            {
                label: 'Chuyển giao',
                icon: 'pi pi-arrow-right', // Icon chuyển giao
                command: () => this.transferVersion(productionInstruction),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_transfer'],
            },
            {
                label: 'Gửi phê duyệt',
                icon: 'pi pi-paperclip', // Icon gửi phê duyệt (ví dụ dùng paperclip)
                command: () => this.sendRequestApproval(productionInstruction),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_submit'],
            },
            {
                label: 'Phê duyệt',
                icon: 'pi pi-check', // Icon phê duyệt
                command: () => this.approveVersion(productionInstruction),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_approve'],
            },

            {
                label: 'Xóa',
                icon: 'pi pi-trash', // Icon xóa
                command: () => this.deleteVersion(productionInstruction),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_delete'],
            },
            {
                label: 'Lịch sử thay đổi',
                icon: 'pi pi-history', // Icon lịch sử
                command: () => this.viewHistory(productionInstruction),
                authority: ['ROLE_SYSTEM_ADMIN', 'version_update_history'],
            },
        ];

        switch (true) {
            case productionInstruction.status === Status['Draft'] && phase === 'prototype':
                return allItems.filter((item) => ['Xem chi tiết', 'Chỉnh sửa', 'Nhân bản', 'Gửi phê duyệt', 'Xóa', 'Lịch sử thay đổi'].includes(item.label));
            case productionInstruction.status === Status['Draft'] && (phase === 'pilot' || phase === 'mp'):
                return allItems.filter((item) => ['Xem chi tiết', 'Chỉnh sửa', 'Nhân bản', 'Gửi phê duyệt', 'Lịch sử thay đổi'].includes(item.label));

            case productionInstruction.status === Status['SentToApprove2'] && (phase === 'pilot' || phase === 'mp' || phase === 'prototype'):
                return allItems.filter((item) => ['Xem chi tiết', 'Nhân bản', 'Phê duyệt', 'Lịch sử thay đổi'].includes(item.label));
            case productionInstruction.status === Status['SentToApprove'] && (phase === 'pilot' || phase === 'mp' || phase === 'prototype'):
                return allItems.filter((item) => ['Xem chi tiết', 'Nhân bản', 'Lịch sử thay đổi'].includes(item.label));
            case productionInstruction.status === Status['Rejected'] && (phase === 'pilot' || phase === 'mp' || phase === 'prototype'):
                return allItems.filter((item) => ['Xem chi tiết', 'Chỉnh sửa', 'Nhân bản', 'Gửi phê duyệt', 'Lịch sử thay đổi'].includes(item.label));
            case productionInstruction.status === Status['Approved'] && phase === 'prototype':
                return allItems.filter((item) => ['Xem chi tiết', 'Phê duyệt', 'Nhân bản', 'Lịch sử thay đổi'].includes(item.label));
            case productionInstruction.status === Status['Approved'] && phase === 'pilot':
                return allItems.filter((item) => ['Xem chi tiết', 'Phê duyệt', 'Nhân bản', 'Lịch sử thay đổi'].includes(item.label));
            case productionInstruction.status === Status['Approved'] && phase === 'mp':
                return allItems.filter((item) => ['Xem chi tiết', 'Nhân bản', 'Lịch sử thay đổi'].includes(item.label));
            default:
                return [];
        }
    }
    editVersion(productionInstruction: any): void {
        this.router.navigate(
            ['/ptm/manufacturing-technology-records/edit', this.currentProduct.id, productionInstruction.versionId, productionInstruction.id],
            {
                state: {
                    currentProduct: this.currentProduct,
                },
            },
        );
    }

    viewDetail(productionInstruction: any): void {
        this.router.navigate(
            ['/ptm/manufacturing-technology-records/view', this.currentProduct.id, productionInstruction.versionId, productionInstruction.id],
            {
                state: {
                    currentProduct: this.currentProduct,
                },
            },
        );
    }

    deleteVersion(v: any): void {
        const versionId = v.id;
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(versionId).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');

                        // reload dữ liệu
                        this.loadVersionProfiles.emit();
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }

    approveVersion(v: any): void {
        this.openApprovalPopup.emit(v);
    }
    sendRequestApproval(v: any): void {
        this.openSendApprovalPopup.emit(v);
    }
    getCheckedVersions(): any[] {
        return this.tableRows.flatMap((r) => this.versionKeys.map((k) => r[k])).filter((v): v is any => !!v && !!v.selected);
    }
    onCheckboxChange() {
        this.getCheckedVersions();
    }

    cloneVersion(v: any) {
        this.openClonePopup.emit(v);
    }
    transferVersion(v: any) {
        this.openTransferPopup.emit(v);
    }
    viewHistory(v: any) {
        this.openHistoryChangedPopup.emit(v);
    }
    compareVersions() {
        // Lấy tất cả Version từ các row, lọc những cái selected = true
        const selected: any[] = this.tableRows.flatMap((row) => this.versionKeys.map((key) => row[key])).filter((v): v is any => !!v && v.selected === true);

        return selected;
    }
}
