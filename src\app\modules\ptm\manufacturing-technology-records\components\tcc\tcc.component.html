<ng-container>

    <div class="p-fluid tw-space-y-2">
        <app-info-share [title]="title"></app-info-share>
    </div>
    <div class="detail-tab tw-mt-4">
        <div class="tw-flex tw-justify-between tw-border-b-2">
            <div> Chi tiết về TCC</div>
            <div class="tw-flex tw-space-x-2">
                <p-button label="Track Changes" size="small"></p-button>
                <p-button label="Xuất excel" size="small"></p-button>
            </div>
        </div>
    </div>
    <div class="tw-grid tw-grid-cols-2 tw-mt-4 tw-gap-4">
        <div>
            <div class="border rounded-md shadow-sm">
                <div class="tw-flex tw-items-center tw-justify-center bg-gray-100 tw-p-2 cursor-pointer"
                     (click)="toggleCollapse()">
                    <div>
                        <span class="font-semibold">Thời gian sản xuất</span>
                        <i class="pi text-lg tw-ml-3" [ngClass]="collapsed ? 'pi-chevron-down' : 'pi-chevron-up'"></i>
                    </div>
                </div>

                <p-table *ngIf="!collapsed" [value]="[{}]" class="p-datatable-sm">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>Số ca làm việc/ ngày</th>
                            <th>Thời gian làm việc/ ca</th>
                            <th>Giải lao/ ca (phút)</th>
                            <th>Ăn trưa/ ca (phút)</th>
                            <th>Ăn bữa phụ/ ca (phút)</th>
                            <th>Thời gian sản xuất/ ca</th>
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-rowData>
                        <tr>
                            <td>
                                <input type="number" pInputText [(ngModel)]="form.soCa" />
                            </td>
                            <td>
                                <div class="flex gap-1">
                                    <input type="number" pInputText [(ngModel)]="form.thoiGianLamViec" class="w-full" />
                                    <p-dropdown
                                        [options]="timeUnits"
                                        [(ngModel)]="form.donViLamViec"
                                        styleClass="w-20"
                                        placeholder="ĐV"
                                    ></p-dropdown>
                                </div>
                            </td>
                            <td>
                                <input type="number" pInputText [(ngModel)]="form.giaiLao" />
                            </td>
                            <td>
                                <input type="number" pInputText [(ngModel)]="form.anTrua" />
                            </td>
                            <td>
                                <input type="number" pInputText [(ngModel)]="form.anPhu" />
                            </td>
                            <td>
                                <div class="flex gap-1">
                                    <input type="number" pInputText [(ngModel)]="form.thoiGianSanXuat" class="w-full" />
                                    <p-dropdown
                                        [options]="timeUnits"
                                        [(ngModel)]="form.donViSanXuat"
                                        styleClass="w-20"
                                        placeholder="ĐV"
                                    ></p-dropdown>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
        </div>
        <div>
            <div class="border rounded-md shadow-sm">
                <div class="flex items-center justify-between bg-gray-100 p-2 cursor-pointer"
                     (click)="toggleCollapse()">
                    <span class="font-semibold">Thời gian sản xuất</span>
                    <i class="pi text-lg" [ngClass]="collapsed ? 'pi-chevron-down' : 'pi-chevron-up'"></i>
                </div>

                <p-table *ngIf="!collapsed" [value]="[{}]" class="p-datatable-sm">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>Số ca làm việc/ ngày</th>
                            <th>Thời gian làm việc/ ca</th>
                            <th>Giải lao/ ca (phút)</th>
                            <th>Ăn trưa/ ca (phút)</th>
                            <th>Ăn bữa phụ/ ca (phút)</th>
                            <th>Thời gian sản xuất/ ca</th>
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-rowData>
                        <tr>
                            <td>
                                <input type="number" pInputText [(ngModel)]="form.soCa" />
                            </td>
                            <td>
                                <div class="flex gap-1">
                                    <input type="number" pInputText [(ngModel)]="form.thoiGianLamViec" class="w-full" />
                                    <p-dropdown
                                        [options]="timeUnits"
                                        [(ngModel)]="form.donViLamViec"
                                        styleClass="w-20"
                                        placeholder="ĐV"
                                    ></p-dropdown>
                                </div>
                            </td>
                            <td>
                                <input type="number" pInputText [(ngModel)]="form.giaiLao" />
                            </td>
                            <td>
                                <input type="number" pInputText [(ngModel)]="form.anTrua" />
                            </td>
                            <td>
                                <input type="number" pInputText [(ngModel)]="form.anPhu" />
                            </td>
                            <td>
                                <div class="flex gap-1">
                                    <input type="number" pInputText [(ngModel)]="form.thoiGianSanXuat" class="w-full" />
                                    <p-dropdown
                                        [options]="timeUnits"
                                        [(ngModel)]="form.donViSanXuat"
                                        styleClass="w-20"
                                        placeholder="ĐV"
                                    ></p-dropdown>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
        </div>
    </div>

</ng-container>
