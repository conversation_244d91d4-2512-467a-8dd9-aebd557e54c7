<ng-container>
    <div class="tw-my-2">
        <label>Chi tiết tài liệu kh<PERSON>c</label>
    </div>
    <div #container *ngIf="formGroup">
        <app-form #form [formGroup]="formGroup" (onSubmit)="onSave()">
            <div formArrayName="items">
                <p-panel [toggleable]="true">
                    <ng-template pTemplate="icons">
                        <button
                            pButton
                            type="button"
                            class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                            appFullscreenToggle
                            [target]="container"
                        ></button>
                    </ng-template>
                    <p-table styleClass="p-datatable-gridlines" [value]="items.controls">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 2rem">STT</th>
                                <th style="min-width: 9rem">Tên tài liệu</th>
                                <th style="min-width: 9rem">Biểu mẫu</th>
                                <th style="min-width: 9rem">Last Update</th>
                                <th style="min-width: 9rem"><PERSON><PERSON><PERSON><PERSON> cập nhật</th>
                                <th style="max-width: 5rem">Thao tác</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr *ngIf="item.get('action')?.value !== 3" [formGroupName]="rowIndex">
                                <!-- STT -->
                                <td>
                                    <app-form-item label=""> {{ rowIndex + 1 }}</app-form-item>
                                </td>
                                <!-- Tên tài liệu -->
                                <td>
                                    <app-form-item label="" validateTrigger="touched">
                                        <input pInputText maxlength="50" type="text" formControlName="name" class="tw-w-full" />
                                    </app-form-item>
                                </td>
                                <!-- Biểu mẫu: upload file -->
                                <td>
                                    <app-form-item label="">
                                        <app-upload-custom
                                            #OtherDocsUploader
                                            [loading]="loadingRows.get(item.get('id')?.value)"
                                            [filePath]="item.get('filePath')?.value"
                                            [initialFileName]="item.get('filePath')?.value"
                                            [limit]="1"
                                            (onChange)="onFileSelected($event, item.get('id')?.value)"
                                            (onClear)="onClearFile(item.get('id')?.value)"
                                            [disabled]="mode === 'view'"
                                        ></app-upload-custom>
                                    </app-form-item>
                                </td>
                                <!-- Last Update -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ item.get('lastUpdateText')!.value }}</span>
                                    </app-form-item>
                                </td>
                                <!-- Người cập nhật -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ item.get('userUpdate')!.value }}</span>
                                    </app-form-item>
                                </td>

                                <td>
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            [disabled]="mode === 'view'"
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            pTooltip="Hủy"
                                            tooltipPosition="top"
                                            type="button"
                                            (click)="removeItem(item.get('id')?.value)"
                                        >
                                            <span class="pi pi-times"></span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="tw-mt-3">
                        <p-button [disabled]="mode === 'view'" label="Thêm" icon="pi pi-plus" severity="info" size="small" (click)="addItem()"></p-button>
                    </div>
                </p-panel>
            </div>
            <app-tab-action-buttons [form]="formGroup" [mode]="mode" [status]="version.status" [isSaving]="isBusy$"></app-tab-action-buttons>
        </app-form>
    </div>
</ng-container>
