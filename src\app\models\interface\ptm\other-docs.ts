export interface OtherDocViewModel {
    id: number;
    name: string;
    file: File | null;
    lastUpdate: string | null;
    lastUpdateText?: string;
    filePath: string;
    userUpdate: string;
    action: 0 | 1 | 2 | 3;
}
export interface OtherDocRequest {
    id: number;
    name: string;
    filePath: string | null;
    userUpdate: string;
    lastUpdate: number | null;
    action: 0 | 1 | 2 | 3;
}
export interface OtherDocsSaveData {
    productInstructionId: string | number;
    payload: UpdateOtherDocsPayload;
}
export interface UpdateOtherDocsPayload {
    productVersionId: number;
    phase: number;
    otherDocuments: OtherDocRequest[];
}
export interface OtherDocDto {
    id: number;
    name: string;
    filePath: string;
    lastUpdate: number;
    userUpdate: string;
    action?: number;
}

export interface UpdatedBy {
    id: number;
    fullName: string;
    email: string;
}
export interface GetOtherDocumentsResponse {
    instructionInfoDto: InstructionInfoDto[];
    otherDocumentDtos: OtherDocDto[];
}
export interface InstructionInfoDto {
    instructionId: number;
}
