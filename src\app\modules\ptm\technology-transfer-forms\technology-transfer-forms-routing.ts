import { canAuthorize } from '../../../core/auth/auth.guard';

export const TTFRouting = {
    path: 'technology-transfer-forms',
    title: 'Phiếu CG CNSX',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
    children: [
        // {
        //     path: '',
        //     title: '<PERSON><PERSON> s<PERSON>ch hồ sơ sản phẩm',
        //     data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
        //     loadComponent: () => import('./list/list.component').then((c) => c.ProductFileListComponent),
        // },
        // {
        //     path: 'create',
        //     title: 'T<PERSON><PERSON> mớ<PERSON> hồ sơ sản phẩm',
        //     data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
        //     loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        // },
        // {
        //     path: 'edit/:productId/:versionId',
        //     title: 'Chỉnh sử<PERSON> hồ sơ sản phẩm',
        //     data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
        //     loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        // },
        // {
        //     path: 'view/:productId/:versionId',
        //     title: 'Xem chi tiết hồ sơ sản phẩm',
        //     data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
        //     loadComponent: () => import('./edit/product-file.edit.component').then((c) => c.ProductFileEditComponent),
        // },
    ],
};
