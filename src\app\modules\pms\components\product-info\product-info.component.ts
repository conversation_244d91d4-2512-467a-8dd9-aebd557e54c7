import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { TagModule } from 'primeng/tag';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { ProductDetail } from 'src/app/models/interface/pms';
import { environment } from 'src/environments/environment';
import { DropdownModule } from 'primeng/dropdown';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { FormsModule } from '@angular/forms';
import { distinctUntilChanged, Subject, Subscription } from 'rxjs';
type FieldType = 'text' | 'link' | 'tag' | 'select' | 'input';
interface Field {
    label: string;
    /** giá trị hiển thị */
    value: keyof ProductDetail;
    /** kiểu render */
    type?: 'text' | 'link' | 'tag' | 'select' | 'input';
    /** chỉ dùng khi type==='select' */
    options?: { label: string; value: any }[];
    /** chỉ dùng khi type==='input' */
    placeholder?: string;
    editable?: boolean;
    /** chỉ dùng khi type==='link' */
    url?: keyof ProductDetail;
}
@Component({
    selector: 'app-product-info',
    standalone: true,
    imports: [CommonModule, PanelModule, TagModule, TableModule, ButtonModule, MenuModule, DropdownModule, FormsModule, ComboboxNonRSQLComponent],
    templateUrl: './product-info.component.html',
    styleUrls: ['./product-info.component.scss'],
})
export class ProductInfoComponent {
    @Input() product: ProductDetail;
    @Input() emptyMessage: string = 'Vui lòng chọn sản phẩm để tiếp tục';
    @Input() forceShow: boolean = false;
    @Input() fieldsLeft: Field[] = [];
    @Input() fieldsRight: Field[] = [];
    @Input() header: string;
    @Input() class = '';
    @Input() style: { [klass: string]: any } | null = null;
    @Output() fieldChanged = new EventEmitter<{ field: keyof ProductDetail; value: any; selected: any }>();
    fieldChanged$ = new Subject<{ field: keyof ProductDetail; value: any; selected: any }>();
    private sub: Subscription;
    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    getProductValue(field: string) {
        return this.product ? this.product[field] ?? null : null;
    }
    setProductValue(field: string, value: any) {
        if (!this.product || typeof this.product !== 'object') {
            // Nếu muốn auto khởi tạo lại, thì:
            this.product = {};
        }
        if (value === null || value === undefined) {
            delete this.product[field]; // hoặc this.product[field] = null;
        } else {
            this.product[field] = value;
        }
    }
    ngOnInit() {
        this.sub = this.fieldChanged$
            .pipe(
                // chỉ emit khi giá trị thực sự đổi (so sánh theo field và value)
                distinctUntilChanged((prev, curr) => prev.field === curr.field && prev.value === curr.value),
            )
            .subscribe((evt) => {
                this.fieldChanged.emit(evt);
            });
    }

    ngOnDestroy() {
        this.sub?.unsubscribe();
    }
    hasProductInfo(): boolean {
        return this.forceShow || !!this.product;
    }
}
