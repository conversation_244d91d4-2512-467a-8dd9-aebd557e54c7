import { Component, Input, Output, EventEmitter, On<PERSON>nit, On<PERSON>estroy, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { <PERSON>B<PERSON>, ManBomDetail } from 'src/app/models/interface/ptm';
import { Observable } from 'rxjs';
import { InputTextModule } from 'primeng/inputtext';
import { MANBOM_PROCESS_TYPE } from 'src/app/models/constant/ptm';
import { DropdownModule } from 'primeng/dropdown';

@Component({
    selector: 'app-manbom',
    standalone: true,
    templateUrl: './manbom.component.html',
    styleUrls: ['./manbom.component.scss'],
    imports: [
        CommonModule,
        FormsModule,
        InputTextareaModule,
        InfoShareComponent,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        DropdownModule,
    ],
})
export class ManbomComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() isSaving!: Observable<boolean>;
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    processType = MANBOM_PROCESS_TYPE;
    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();

    ngOnDestroy(): void {
        console.log('🧹 [ManbomComponent] Unmounted');
    }
    ngOnInit(): void {
        console.log('🧹 [ManbomComponent] Init');
        this.initForm();
    }
    formGroup: FormGroupCustom<ManBom>;
    constructor(private fb: FormBuilder) {
        this.formGroup = new FormGroupCustom<ManBom>(this.fb, {
            listManBom: new FormArrayCustom([]),
        });
    }

    initForm() {
        this.formGroup = new FormGroupCustom<ManBom>(this.fb, {
            listManBom: new FormArrayCustom([]),
        });
    }

    get manBom(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listManBom') as FormArrayCustom<FormGroup>;
    }

    initFormContact(items: ManBomDetail[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                }),
        );
    }

    addItem() {
        let newItem = null;
        newItem = new FormGroupCustom(this.fb, {
            id: [null],
            description: [null],
            unit: [null],
            process: [null],
            quantity: [null],
            reference: [null],
            materialType: [null],
            attritionRate: [null],
            note: [null],
        });
        this.manBom.push(newItem);
    }

    removeItem(index: number) {
        this.manBom.removeAt(index);
    }

    handleSubmit() {
        if (this.formGroup.invalid) return;
        this.submitted.emit(this.formGroup.value);
    }
    onSubmit() {
        if (this.formGroup.invalid) return;
        this.submitted.emit(this.formGroup.value);
    }
}
