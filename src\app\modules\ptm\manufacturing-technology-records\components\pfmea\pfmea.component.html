<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share
            [title]="title"
            (changeValueApprover)="handleApproverChange($event)"
            [name]="nameTab"
            [detailInfo]="detailPfmea"
        ></app-info-share>
    </div>
    <div class="p-fluid tw-space-y-2">
        <div class="tw-flex tw-justify-between tw-border-b-2">
            <label class="label-tab-cnsx">Chi tiết PFMEA</label>
            <div class="tw-flex tw-space-x-2">
                <p-button label="Track Changes" size="small" (click)="openTrackDialog(3)" ></p-button>
                <p-button label="Xuất excel" size="small"></p-button>
            </div>
        </div>
    </div>
    <app-form-item label="">
        <app-form #form [formGroup]="formGroup" layout="vertical">
            <div formArrayName="pfmeas">
                <p-table styleClass="p-datatable-gridlines" [value]="listPFMEA.controls">
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="min-width: 5rem ;text-align: center ">STT</th>
                            <th *ngFor="let col of columns"
                                [style.minWidth]="col.minWidth"
                                [style.text-align]="'center'"
                                [style.maxWidth]="col.maxWidth">
                                {{ col.header }}
                            </th>
                            <th style=" text-align: center">Thao tác</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                        <tr [formGroupName]="rowIndex">
                            <td>
                                <app-form-item label=""> {{ rowIndex + 1 }}</app-form-item>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <p-dropdown
                                        [options]="linePfmea"
                                        formControlName="line"
                                        optionLabel="label"
                                        optionValue="value"
                                        class="tw-w-full"
                                        [showClear]="true"
                                        appendTo="body"
                                    />
                                </app-form-item>
                            </td>

                            <td>
                                <app-form-item label="">
                                    {{ item.get('oc')?.value }}
                                </app-form-item>
                            </td>
                            <td>
                                <app-form-item label="">
                                    {{ item.get('od')?.value }}
                                </app-form-item>
                            </td>
                            <td>
                                <app-form-item label="">
                                    <input type="text" class="tw-w-full" pInputText maxlength="100"
                                           formControlName="target" />
                                </app-form-item>
                            </td>
                            <td class="tw-flex tw-justify-center">
                                <button pButton type="button" (click)="openPanel(rowIndex, op, $event)"
                                        label="Thêm/sửa lỗi"></button>
                                <p-overlayPanel #op [dismissable]="false" [style]="{ width: '90vw' }" appendTo="body">
                                    <form [formGroup]="item">
                                        <div formArrayName="pfmeaErrors">
                                            <p-table styleClass="p-datatable-gridlines"
                                                     [value]="getVisibleErrors(item.get('pfmeaErrors'))"

                                            >
                                                <ng-template pTemplate="header">
                                                    <tr>
                                                        <th style=" text-align: center ">STT</th>
                                                        <th *ngFor="let col of columnErrorTable"
                                                            [style.text-align]="'center'"
                                                            [style.maxWidth]="col.maxWidth">
                                                            {{ col.header }}
                                                        </th>
                                                        <th style=" text-align: center">
                                                            <button pButton icon="pi pi-plus" (click)="addRow(rowIndex)"
                                                                    class="p-button-text"></button>
                                                        </th>
                                                    </tr>
                                                </ng-template>
                                                <ng-template pTemplate="body" let-row let-j="rowIndex">
                                                        <tr [formGroup]="row">
                                                            <td class="tw-text-center"
                                                                style="text-align: center">{{ rowIndex + 1 }}
                                                                .{{ j + 1 }}
                                                            </td>

                                                            <!-- Lỗi phát sinh -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText formControlName="error"
                                                                           maxlength="200"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Ảnh hưởng -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText
                                                                           formControlName="errorAffect" maxlength="200"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- SEV -->
                                                            <td class="tw-min-w-[150px]">
                                                                <app-form-item label="" validateTrigger="touched">
                                                                    <input type="text" pInputText formControlName="sev"
                                                                           maxlength="2"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Special Char -->
                                                            <td>
                                                                <app-form-item label="">
                                                                    <p-checkbox
                                                                        name="isSymbol"
                                                                        [binary]="true"
                                                                        formControlName="isSymbol">
                                                                    </p-checkbox>
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Nguyên nhân -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText
                                                                           formControlName="errorCause" maxlength="200"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Biện pháp phòng ngừa -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText
                                                                           formControlName="prevention" maxlength="200"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- OCC -->
                                                            <td class="tw-min-w-[150px]">
                                                                <app-form-item label="" validateTrigger="touched">
                                                                    <input type="text" pInputText formControlName="occ"
                                                                           maxlength="2" pattern="\d*"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Biện pháp kiểm soát -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText
                                                                           formControlName="detection" maxlength="200"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- DET -->
                                                            <td class="tw-min-w-[150px]">
                                                                <app-form-item label="" validateTrigger="touched">
                                                                    <input type="text" pInputText formControlName="det"
                                                                           maxlength="2"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- RPN1 (calculated) -->
                                                            <td>
                                                                <app-form-item label="">
                                                                    {{ calculateRPN(row.get('sev')?.value, row.get('occ')?.value, row.get('det')?.value) }}
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Hành động điều chỉnh -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText
                                                                           formControlName="correctionAction"
                                                                           maxlength="200"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Trách nhiệm -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText
                                                                           formControlName="responsibility" maxlength="100"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Ngày hoàn thành -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <p-calendar formControlName="finishDate"
                                                                                dateFormat="dd/mm/yy"
                                                                                showIcon
                                                                                inputStyleClass="w-full">
                                                                    </p-calendar>
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Kết quả hành động -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText formControlName="result"
                                                                           maxlength="100"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Hành động -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <input type="text" pInputText formControlName="action"
                                                                           maxlength="100"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- Ngày hiệu lực -->
                                                            <td class="tw-min-w-[200px]">
                                                                <app-form-item label="">
                                                                    <p-calendar formControlName="effectiveDate"
                                                                                dateFormat="dd/mm/yy"
                                                                                showIcon
                                                                                inputStyleClass="w-full">
                                                                    </p-calendar>
                                                                </app-form-item>
                                                            </td>

                                                            <!-- SEV After -->
                                                            <td class="tw-min-w-[150px]">
                                                                <app-form-item label="" validateTrigger="touched">
                                                                    <input type="text" pInputText formControlName="sevAfter"
                                                                           maxlength="2"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- OCC After -->
                                                            <td class="tw-min-w-[150px]">
                                                                <app-form-item label="" validateTrigger="touched">
                                                                    <input type="text" pInputText formControlName="occAfter"
                                                                           maxlength="2"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- DET After -->
                                                            <td class="tw-min-w-[150px]">
                                                                <app-form-item label="" validateTrigger="touched">
                                                                    <input type="text" pInputText formControlName="detAfter"
                                                                           maxlength="2"
                                                                           class="tw-w-full" />
                                                                </app-form-item>
                                                            </td>

                                                            <!-- RPN2 (calculated) -->

                                                            <td>{{ calculateRPN(row.get('sevAfter')?.value, row.get('occAfter')?.value, row.get('detAfter')?.value) }}</td>

                                                            <td>
                                                                <button pButton icon="pi pi-minus"
                                                                        (click)="removeRow(rowIndex,j)"
                                                                        class="p-button-text text-red-500"></button>
                                                            </td>

                                                        </tr>

                                                </ng-template>
                                            </p-table>
                                        </div>
                                    </form>
                                    <div class="tw-flex tw-justify-center tw-mt-2">
                                        <button pButton label="Lưu" class="tw-mr-2"
                                                (click)="savePanel(rowIndex, op)"></button>
                                        <button pButton label="Đóng" class="tw-mr-2"
                                                (click)="cancelPanel(rowIndex, op)"></button>
                                    </div>
                                </p-overlayPanel>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
            <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
                <p-button label="Lưu" type="submit" [loading]="isSaving | async" (onClick)="handleSubmit($event)"
                          loadingIcon="pi pi-spinner pi-spin">
                </p-button>
                <p-button
                    label="Gửi Preview"
                    type="button"
                    [disabled]="isSubmitting | async"
                    [loading]="isSubmitting | async"
                    loadingIcon="pi pi-spinner pi-spin"
                >
                </p-button>
                <p-button label="Phê duyệt" type="button" [disabled]="isApproving | async"
                          [loading]="isApproving | async" loadingIcon="pi pi-spinner pi-spin">
                </p-button>
            </div>
        </app-form>
    </app-form-item>
    <ng-container *ngIf="showTrackDialog">
        <app-track-changes
            [visible]="showTrackDialog"
            [recordId]="selectedTab"
            (closed)="handleCloseTrackDialog()"
        ></app-track-changes>
    </ng-container>
</ng-container>
