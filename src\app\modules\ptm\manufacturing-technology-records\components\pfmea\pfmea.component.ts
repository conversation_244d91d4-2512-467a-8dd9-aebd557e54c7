import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON><PERSON>, ViewChild, SimpleChang<PERSON>, inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractControl, FormArray, FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { PFMEA, PFMEADetail, ProcessFlow, ProcessFlowDetail } from 'src/app/models/interface/ptm';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { SYMBOL_TYPE, PRODUCT_PROCESS_TYPE, LINE_PFMEA } from 'src/app/models/constant/ptm';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { InfoShareComponent } from '../info-share/info-share.component';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { numberValidator } from '../../../../../utils/validator';
import { combineLatest, Observable } from 'rxjs';
import { Pfmea, PfmeaError } from '../../../../../models/interface/ptm/pfmea';
import { PfmeaService } from '../../../../../services/ptm/pfmea/pfmea.service';
import { MessageService } from 'primeng/api';
import { TrackChangesComponent } from '../track-changes/track-changes.component';

@Component({
    selector: 'app-pfmea',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        InfoShareComponent,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        DropdownModule,
        CheckboxModule,
        OverlayPanelModule,
        CalendarModule,
        TrackChangesComponent,
    ],
    templateUrl: './pfmea.component.html',
    styleUrls: ['./pfmea.component.scss'],
})
export class PfmeaComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() isSaving: Observable<boolean>;
    @Input() isSubmitting: Observable<boolean>;
    @Input() isApproving: Observable<boolean>;
    @Input() productInstructionId: number;
    @Input() currentProduct: any;
    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();
    @Output() changed = new EventEmitter<string>();

    pfmeaService = inject(PfmeaService);
    cdr = inject(ChangeDetectorRef);
    messageService = inject(MessageService);
    nameTab = '';

    columnErrorTable = [
        { field: 'loiPhatSinh', header: 'Lỗi phát sinh', maxWidth: '60px' },
        { field: 'anhHuong', header: 'Ảnh hưởng của lỗi', maxWidth: '12rem' },
        { field: 'sev', header: 'SEV.', maxWidth: '6rem' },
        { field: 'specialChar', header: 'Special Char. Symbol (*)', type: 'checkbox', maxWidth: '8rem' },
        { field: 'nguyenNhan', header: 'Nguyên nhân/ Cơ chế phát sinh lỗi', maxWidth: '14rem' },
        { field: 'prevention', header: 'Current Process Controls (Prevention)', maxWidth: '14rem' },
        { field: 'occ', header: 'OCC', maxWidth: '6rem' },
        { field: 'detection', header: 'Current Process Controls (Detection)', maxWidth: '14rem' },
        { field: 'det', header: 'DET.', maxWidth: '6rem' },
        { field: 'rpn', header: 'R.P.N', maxWidth: '6rem' },
        { field: 'hanhDongDieuChinh', header: 'Hành động điều chỉnh', maxWidth: '14rem' },
        { field: 'trachNhiem', header: 'Trách nhiệm', maxWidth: '10rem' },
        { field: 'ngayHoanThanh', header: 'Ngày hoàn thành', type: 'date', maxWidth: '10rem' },
        { field: 'ketQuaHanhDong', header: 'Kết quả hành động', maxWidth: '10rem' },
        { field: 'hanhDong', header: 'Hành động', maxWidth: '10rem' },
        { field: 'ngayHieuLuc', header: 'Ngày hiệu lực', type: 'date', maxWidth: '10rem' },
        { field: 'sev2', header: 'SEV.', maxWidth: '6rem' },
        { field: 'occ2', header: 'OCC', maxWidth: '6rem' },
        { field: 'det2', header: 'DET.', maxWidth: '6rem' },
        { field: 'rpn2', header: 'RPN', maxWidth: '6rem' },
    ];

    columns = [
        { field: 'step', header: 'Dây chuyền/Line', minWidth: '9rem' },
        { field: 'oc', header: 'OC', minWidth: '9rem' },
        { field: 'nextOC', header: 'Operation Description', minWidth: '9rem' },
        { field: 'symbol', header: 'Tiêu chuẩn/Yêu cầu/Mục tiêu', minWidth: '9rem' },
    ];

    clonedPfmeaErrors: { [index: number]: any[] } = {};

    formGroup: FormGroup;
    formErrorTable: FormGroup;
    linePfmea = LINE_PFMEA;

    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    detailPfmea: any;
    showTrackDialog: boolean;
    selectedTab: number =1;

    constructor(private fb: FormBuilder) {
        this.formGroup = this.fb.group({
            pfmeas: this.fb.array([]),
            instructionId: [''],
            reviewerIds: [''],
        });
    }

    get listPFMEA(): FormArray {
        return this.formGroup.get('pfmeas') as FormArray;
    }

    ngOnInit(): void {
        this.nameTab = 'PFMEA-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
        this.formGroup.get('instructionId').setValue(this.productInstructionId);
        console.log(this.productInstructionId);
        if (this.productInstructionId === 0) {
            this.listPFMEA.push(this.initDataFormPfmea());
        } else {
            this.getDataPfmeaUpdate(this.productInstructionId);
        }
    }

    loadFromApi(apiResponse: any): void {
        this.listPFMEA.clear(); // clear existing if any
        apiResponse.forEach((pfmea) => {
            this.listPFMEA.push(this.setDataFormPfmea(pfmea));
        });
    }

    getDataPfmeaUpdate(id) {
        console.log('chạy init');
        this.pfmeaService.getPfmea(id).subscribe({
            next: (res) => {
                const dataInstructionInfo = { instructionInfo: res.instructionInfo };
                this.detailPfmea = { ...dataInstructionInfo };
                const reviewers = res?.instructionInfo?.reviewers?.map((r) => r.id).join(',');
                this.formGroup.get('reviewerIds').setValue(reviewers);
                this.loadFromApi(res.pfmeas);
                console.log(this.detailPfmea);
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    updatePfmea(id, data) {
        this.pfmeaService.updatePfmea(id, data).subscribe({
            next: (res) => {
                if (this.productInstructionId !== 0) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật PFMEA thành công',
                    });
                } else {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo PFMEA thành công',
                    });
                }
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    setDataFormPfmea(data: Pfmea): FormGroup {
        return this.fb.group({
            id: [data.id],
            instructionInfoId: [data.instructionInfoId],
            processFlowsId: [data.processFlowId],
            oc: [data.oc],
            od: [data.od],
            line: [data.line],
            target: [data.target],
            pfmeaErrors: this.fb.array((data.pfmeaErrors || []).map((error) => this.setDataFormPfmeaError(error))),
        });
    }

    setDataFormPfmeaError(error: PfmeaError): FormGroup {
        return this.fb.group({
            id: [error.id],
            pfmeaId: [error.pfmeaId],
            error: [error.error],
            errorAffect: [error.errorAffect],
            sev: [error.sev, numberValidator()],
            isSymbol: [error.isSymbol],
            errorCause: [error.errorCause],
            prevention: [error.prevention],
            occ: [error.occ, numberValidator()],
            detection: [error.detection],
            det: [error.det, numberValidator()],
            correctionAction: [error.correctionAction],
            responsibility: [error.responsibility],
            finishDate: [new Date(error.finishDate)],
            result: [error.result],
            action: [error.action],
            effectiveDate: [new Date(error.effectiveDate)],
            actionPayload: [2],
            sevAfter: [error.sevAfter, numberValidator()],
            occAfter: [error.occAfter, numberValidator()],
            detAfter: [error.detAfter, numberValidator()],
        });
    }

    initDataFormPfmea(): FormGroup {
        const pfmeaErrors = this.fb.array([]) as FormArray;
        pfmeaErrors.push(this.initRowErrorTable());
        return this.fb.group({
            id: [],
            instructionInfoId: [],
            processFlowsId: [],
            oc: [],
            od: [],
            line: [],
            target: [],
            pfmeaErrors: pfmeaErrors,
        });
    }

    initRowErrorTable(): FormGroup {
        return this.fb.group({
            id: [null], // optional nếu là tạo mới
            pfmeaId: [null], // optional nếu cần set sau

            error: [''], // error = loiPhatSinh
            errorAffect: [''], // anhHuong
            sev: ['', numberValidator()], // sev1
            occ: ['', numberValidator()], // occ1
            det: ['', numberValidator()], // det1

            isSymbol: [null], // specialChar
            errorCause: [''], // nguyenNhan
            prevention: [''], // preventControl
            detection: [''], // detectControl

            correctionAction: [''], // hanhDongDieuChinh
            responsibility: [''], // trachNhiem
            finishDate: [''], // ngayHoanThanh
            result: [''], // ketQuaHanhDong
            action: [''], // hanhDong
            effectiveDate: [''], // ngayHieuLuc
            actionPayload: [1],

            sevAfter: ['', numberValidator()], // sev2
            occAfter: ['', numberValidator()], // occ2
            detAfter: ['', numberValidator()], // det2
        });
    }

    openPanel(index: number, overlay: OverlayPanel, event: MouseEvent): void {
        const errors = this.listPFMEA.at(index).get('pfmeaErrors') as FormArray;
        this.clonedPfmeaErrors[index] = errors.value.map((e) => ({ ...e })); // deep copy
        setTimeout(() => {
            overlay.toggle(event);
            this.cdr.detectChanges(); // tùy chọn, có thể bỏ nếu dùng setTimeout
        }, 0);
    }

    cancelPanel(index: number, overlay: OverlayPanel): void {
        const errors = this.listPFMEA.at(index).get('pfmeaErrors') as FormArray;

        errors.clear(); // xoá tất cả dòng hiện tại

        // Gán lại dữ liệu từ bản sao ban đầu
        const oldErrors = this.clonedPfmeaErrors[index] || [];
        for (const err of oldErrors) {
            errors.push(this.fb.group({ ...err })); // hoặc dùng createPFMEAErrorGroup() nếu có sẵn
        }
        overlay.hide();
    }

    savePanel(index: number, overlay: OverlayPanel): void {
        if (!this.validatePFMEAErrors(index)) {
            return;
        }
        const pfmeaGroup = this.listPFMEA.at(index);
        const errors = pfmeaGroup.get('pfmeaErrors') as FormArray;

        if (errors.invalid) {
            errors.markAllAsTouched();
            return;
        }

        // ✅ Nếu bạn muốn xử lý lưu dữ liệu tại đây, ví dụ gửi API:
        const payload = errors.value;
        console.log('Lưu pfmeaErrors của dòng:', index, payload);

        overlay.hide(); // ✅ Đóng overlay sau khi lưu
    }

    validatePFMEAErrors(index: number): boolean {
        const pfmeaGroup = this.listPFMEA.at(index);
        const errorsArray = pfmeaGroup.get('pfmeaErrors') as FormArray;

        let isValid = true;
        errorsArray.controls.forEach((ctrl) => {
            if (ctrl.invalid) {
                isValid = false;
                ctrl.markAllAsTouched(); // đánh dấu để hiển thị lỗi
            }
        });

        return isValid;
    }

    ngOnDestroy(): void {
        console.log('🧹 [ProcessFlowComponent] Unmounted');
    }

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    sortProcessFlow() {}

    onSubmit(value: ProcessFlow): void {
        console.log(value, value.listProcessFlow);
    }

    getData() {
        return this.formGroup.getRawValue();
    }

    validateProcess() {
        // console.log('validateProcess', this.formGroup.valid);
        return this.formGroup.valid;
    }

    errorProcess() {
        console.log('errorProcess');
    }

    handleSubmit(event) {
        // Gọi callback nếu có

        // Emit event nếu cha lắng nghe
        this.submitted.emit({
            content: this.content,
            config: this.config,
            items: this.items,
        });

        const formData = this.formatPfmeaDates(this.formGroup.getRawValue());
        const fomatFormData = this.formatPfmeaDates(formData);
        this.updatePfmea(this.productInstructionId, fomatFormData);

        console.log(this.formGroup.getRawValue());
    }

    formatPfmeaDates(formData: any): any {
        return {
            ...formData,
            pfmeas:
                formData.pfmeas?.map((pfmea: any) => {
                    // destructuring loại bỏ oc và od
                    const { oc, od, rpn1, rpn2, ...restPfmea } = pfmea;

                    return {
                        ...restPfmea,
                        pfmeaErrors:
                            pfmea.pfmeaErrors?.map((error: any) => ({
                                ...error,
                                finishDate: error.finishDate ? new Date(error.finishDate).getTime() : null,
                                effectiveDate: error.effectiveDate ? new Date(error.effectiveDate).getTime() : null,
                            })) || [],
                    };
                }) || [],
        };
    }

    getVisibleErrors(errors: AbstractControl | null | undefined): FormGroup[] {
        if (!errors || !(errors instanceof FormArray)) return [];
        return errors.controls.filter((ctrl) => ctrl.get('actionPayload')?.value !== 3) as FormGroup[];
    }

    addRow(pfmeaIndex: number): void {
        const errors = this.listPFMEA.at(pfmeaIndex).get('pfmeaErrors') as FormArray;
        const newRow = this.initRowErrorTable();
        errors.push(newRow);
    }

    removeRow(pfmeaIndex: number, visibleErrorIndex: number): void {
        const pfmeaErrors = this.listPFMEA.at(pfmeaIndex).get('pfmeaErrors') as FormArray;

        const visibleErrors = this.getVisibleErrors(pfmeaErrors);
        const rowToRemove = visibleErrors[visibleErrorIndex];

        // Nếu là bản ghi mới (chưa có id) → xóa hẳn khỏi FormArray
        if (!rowToRemove.get('id')?.value) {
            const actualIndex = pfmeaErrors.controls.indexOf(rowToRemove);
            if (actualIndex !== -1) {
                pfmeaErrors.removeAt(actualIndex);
            }
        } else {
            // Bản ghi cũ → xóa mềm (ẩn đi)
            rowToRemove.get('actionPayload')?.setValue(3);
        }
    }

    calculateRPN(sev?: any, occ?: any, det?: any): number {
        const s = parseInt(sev, 10);
        const o = parseInt(occ, 10);
        const d = parseInt(det, 10);
        if (!isNaN(s) && !isNaN(o) && !isNaN(d)) {
            return Math.max(1, Math.round(s * o * d));
        }
        return 0;
    }

    save() {
        console.log(this.formErrorTable.value);
    }

    handleApproverChange(event) {
        const reviewers = event.map((item: any) => item.id).join(',');
        console.log(reviewers);
        this.formGroup.get('reviewerIds').setValue(reviewers);
    }

    openTrackDialog(tab: number): void {
        this.showTrackDialog = true;
        this.selectedTab = tab;
    }

    handleCloseTrackDialog(): void {
        this.showTrackDialog = false;
    }

}
