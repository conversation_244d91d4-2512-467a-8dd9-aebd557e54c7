import {
    Component,
    Input,
    Output,
    EventEmitter,
    ViewChild,
    ViewChildren,
    ViewContainerRef,
    ComponentRef,
    QueryList,
    Type,
    AfterViewInit,
    OnDestroy,
    ElementRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TabViewModule } from 'primeng/tabview';

export interface TabItem {
    id: string; // định danh tab
    label: string; // tiêu đề hiển thị
    component: Type<any>; // component để render
    inputs?: Record<string, any>; // props truyền vào
    outputs?: Record<string, Function>; // truyền callback @Output
    disabled?: boolean;
    icon?: string;
}

@Component({
    selector: 'app-tab-view',
    standalone: true,
    imports: [CommonModule, TabViewModule],
    templateUrl: './tab-view-dynamic.component.html',
    styleUrls: ['./tab-view-dynamic.component.scss'],
})
export class TabViewComponent implements AfterViewInit, OnDestroy {
    @Input() tabs: TabItem[] = [];
    @Input() activeIndex: number = 0;
    @Output() activeIndexChange = new EventEmitter<number>();

    @ViewChildren('container', { read: ViewContainerRef }) containers!: QueryList<ViewContainerRef>;

    private componentRefs: ComponentRef<any>[] = [];

    ngAfterViewInit(): void {
        // setTimeout(() => this.loadAllTabs());
        setTimeout(() => this.loadActiveTab());
    }

    ngOnDestroy(): void {
        this.componentRefs.forEach((ref) => ref.destroy());
    }
    private loadAllTabs(): void {
        this.containers?.forEach((container, index) => {
            if (!container || this.componentRefs[index]) return;

            const tab = this.tabs[index];
            const ref = container.createComponent(tab.component);

            if (tab.inputs) {
                for (const [key, value] of Object.entries(tab.inputs)) {
                    ref.instance[key] = value;
                }
            }

            if (tab.outputs) {
                for (const [key, handler] of Object.entries(tab.outputs)) {
                    if (ref.instance[key] && typeof ref.instance[key].subscribe === 'function') {
                        ref.instance[key].subscribe(handler);
                    }
                }
            }

            this.componentRefs[index] = ref;
        });
    }
    private loadActiveTab(): void {
        const container = this.containers?.get(this.activeIndex);
        if (!container || this.componentRefs[this.activeIndex]) return;

        const tab = this.tabs[this.activeIndex];
        const ref = container.createComponent(tab.component);

        if (tab.inputs) {
            for (const [key, value] of Object.entries(tab.inputs)) {
                ref.instance[key] = value;
            }
        }

        if (tab.outputs) {
            for (const [key, handler] of Object.entries(tab.outputs)) {
                if (ref.instance[key] && typeof ref.instance[key].subscribe === 'function') {
                    ref.instance[key].subscribe(handler);
                }
            }
        }

        this.componentRefs[this.activeIndex] = ref;
    }

    onTabChange(event: { index: number }) {
        this.activeIndex = event.index;
        this.activeIndexChange.emit(this.activeIndex);
        this.loadActiveTab();
    }

    /** Gọi instance của component bằng id tab */
    getInstanceById<T>(id: string): T | null {
        const index = this.tabs.findIndex((t) => t.id === id);
        return index >= 0 ? this.componentRefs[index]?.instance : null;
    }

    /** Gọi method bất kỳ từ component con nếu đã được tạo */
    invokeMethod<T = any>(id: string, methodName: string, ...args: any[]): T | null {
        const instance = this.getInstanceById<any>(id);
        if (instance && typeof instance[methodName] === 'function') {
            return instance[methodName](...args);
        }
        return null;
    }

    /** Gọi method getData() của tất cả component đã render */
    getAllData(): any[] {
        return this.componentRefs.filter((ref) => ref).map((ref) => ref.instance?.getData?.());
    }
}
