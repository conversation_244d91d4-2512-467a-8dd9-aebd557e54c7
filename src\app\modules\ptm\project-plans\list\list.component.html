<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button routerLink="create" label="Thêm mới" />
    </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <app-table-common
        [tableId]="tableId"
        [columns]="columns"
        [data]="state.data"
        [loading]="state.isFetching"
        [filterTemplate]="filterTemplate"
        [deleteButton]="true"
        [inactiveBtn]="true"
        [hideButtonHeader]="true"
        [authoritiesDelete]="['ROLE_SYSTEM_ADMIN']"
        [actionTemplate]="actionsTpl"
        [selectionMode]="null"
        [stt]="true"
    >
        <ng-template #filterTemplate>
            <tr>
                <th></th>
                <th [appFilter]="[tableId, 'projectName']">
                    <app-filter-table [tableId]="tableId" field="projectName" placeholder="Tên dự án"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'productName']">
                    <app-filter-table [tableId]="tableId" field="productName" placeholder="Tên sản phẩm"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'vnptManPn']">
                    <app-filter-table [tableId]="tableId" field="vnptManPn" placeholder="VNPT Man P/N"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'startDate']">
                    <app-filter-table [tableId]="tableId" type="date-range" field="startDate" placeholder="Timeline"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'rate']">
                    <app-filter-table [tableId]="tableId" field="rate" placeholder="Tỷ lệ hoàn thành"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'milestoneMultilevelNumbering']">
                    <app-filter-table [tableId]="tableId" field="milestoneMultilevelNumbering" placeholder="Milestone"></app-filter-table>
                </th>
                <th [appFilter]="[tableId, 'approvalStatus']">
                    <app-filter-table
                        [tableId]="tableId"
                        field="approvalStatus"
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'value',
                            fieldLabel: 'label',
                            options: [
                                {
                                    label: 'Draft',
                                    value: '1',
                                },
                                {
                                    label: 'Sent to Approved',
                                    value: '2',
                                },
                                {
                                    label: 'Rejected',
                                    value: '3',
                                },
                                {
                                    label: 'Approved',
                                    value: '4',
                                },
                            ],
                        }"
                        placeholder="Trạng thái"
                    ></app-filter-table>
                </th>
                <th></th>
            </tr>
        </ng-template>
        <ng-template #actionsTpl let-row>
            <td style="white-space: nowrap; text-align: center">
                <p-menu #ActionMenu [popup]="true" [model]="itemsAction" appendTo="body"></p-menu>
                <!-- Nút bật menu -->
                <button
                    pButton
                    type="button"
                    icon="pi pi-ellipsis-h"
                    class="p-button-text p-button-sm"
                    (click)="onMenuClick($event, row)"
                    title="Thao tác"
                ></button>
            </td>
        </ng-template>
        <ng-template #templateActive let-row>
            <p-tag [severity]="getSeverity(row.active)" [value]="getStateText(row.active)"></p-tag>
        </ng-template>
        <ng-template #timelineTpl let-row> {{ row.startDate | date: 'dd/MM/yyyy' }} - {{ row.endDate | date: 'dd/MM/yyyy' }} </ng-template>
        <ng-template #approvalStatusTpl let-row>
            {{ statusMap[row.approvalStatus] }}
        </ng-template>
    </app-table-common>
</div>

<app-popup #HistoryPopup header="THEO DÕI LỊCH SỬ THAY ĐỔI" [isButtonVisible]="false" dialogWidth="60vw" [showConfirmButton]="false">
    <app-table-custom [columns]="trackCols" [data]="historyData">
        <ng-template TableCell="details" let-product>
            <div style="white-space: pre-line; line-height: 1.5">{{ product.details }}</div>
        </ng-template>
    </app-table-custom>
</app-popup>

<app-popup
    #SendApprovalPopup
    [dialogWidth]="'60vw'"
    header="Gửi phê duyệt HS CNSX"
    severity="success"
    [isButtonVisible]="false"
    [formGroup]="formSendApprovalPopup"
    (onSubmit)="submitFormSendApproval($event)"
>
    <app-form [formGroup]="formSendApprovalPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4">
        <div class="tw-grid tw-grid-cols-2 tw-gap-4">
            <app-custom-form-item [control]="formSendApprovalPopup.get('approver')" label="Người phê duyệt:">
                <app-combobox-nonRSQL
                    #userSelect
                    [fetchOnInit]="false"
                    type="select-one"
                    formControlName="approver"
                    fieldValue="id"
                    fieldLabel="email"
                    url="/auth/api/users/simple-search"
                    param="query"
                    [additionalParams]="{ page: 0, size: 100 }"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>

            <app-custom-form-item label="Người gửi phê duyệt:">
                <span *ngIf="user$ | async as user" class="tw-leading-8">{{ user.email }} </span>
            </app-custom-form-item>
            <app-custom-form-item label="Ghi chú:">
                <textarea rows="5" class="tw-w-full" pInputTextarea [autoResize]="true" formControlName="note" maxlength="1000"></textarea>
            </app-custom-form-item>
            <app-custom-form-item label="Ngày gửi phê duyệt:">
                <span class="tw-leading-8">
                    {{ today | date: 'dd/MM/yyyy' }}
                </span>
            </app-custom-form-item>
        </div>
    </app-form>
</app-popup>

<p-dialog header="Phê duyệt HSSP" [(visible)]="visibleApprovalPopup" [modal]="true" [style]="{ width: '60vw' }" (onHide)="close('visibleApprovalPopup')">
    <app-form [formGroup]="formApprovalPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formApprovalPopup">
        <div class="tw-grid tw-grid-cols-2 tw-gap-4">
            <app-custom-form-item label="Ghi chú:">
                <span *ngIf="formApprovalPopup.get('noteOfSendApproval')?.value" class="tw-leading-8">
                    {{ formApprovalPopup.get('noteOfSendApproval')?.value }}
                </span></app-custom-form-item
            >

            <app-custom-form-item label="Người gửi phê duyệt:">
                <span *ngIf="formApprovalPopup.get('fromUser')?.value" class="tw-leading-8">{{ formApprovalPopup.get('fromUser')?.value }}</span>
            </app-custom-form-item>
            <a href="#" (click)="onViewHistory($event)" style="cursor: pointer; text-decoration: none"> Xem lịch sử version HS CNSX </a>
            <app-custom-form-item label="Ngày gửi phê duyệt:">
                <span *ngIf="formApprovalPopup.get('created')?.value" class="tw-leading-8">{{
                    formApprovalPopup.get('created')?.value | date: 'dd/MM/yyyy'
                }}</span>
            </app-custom-form-item>
            <app-custom-form-item [control]="formApprovalPopup.get('note')" label="Ghi chú (người phê duyệt):">
                <textarea rows="5" class="tw-w-full" pInputTextarea [autoResize]="true" formControlName="note" maxlength="1000"></textarea>
            </app-custom-form-item>
        </div>
    </app-form>

    <ng-template pTemplate="footer">
        <div class="tw-flex tw-gap-2 tw-justify-center">
            <p-button label="Phê duyệt" size="small" severity="success" (click)="submitFormApproval('CONFIRM')"></p-button>

            <p-button label="Từ chối" size="small" styleClass="p-button-danger" (click)="submitFormApproval('REJECT')"> </p-button>
        </div>
    </ng-template>
</p-dialog>
