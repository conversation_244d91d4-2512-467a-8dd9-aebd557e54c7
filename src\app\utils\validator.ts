import { AbstractControl, FormArray, FormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';
import { differenceInMonths } from 'date-fns';
// Validator kiểm tra trường có phải là yêu cầu hay không
export function requiredValidator(message: string = 'Trường này yêu cầu dữ liệu'): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        return control.value ? null : { required: message };
    };
}

// Validator kiểm tra định dạng email
export function emailValidator(message: string = 'Email không hợp lệ'): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailPattern.test(control.value) ? null : { email: message };
    };
}

// Validator kiểm tra độ dài tối thiểu
export function minLengthValidator(minLength: number, message: string = `Độ dài chuỗi tối thiểu ${minLength} kí tự`): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        return control.value && control.value.trim().length >= minLength ? null : { minlength: message };
    };
}

// Validator kiểm tra độ dài tối đa
export function maxLengthValidator(maxLength: number, message: string = `Độ dài chuỗi tối đa ${maxLength} kí tự`): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        return control.value && control.value.length <= maxLength ? null : { maxlength: message };
    };
}

// Validator kiểm tra độ dài tối đa
export function minValidator(min: number, message: string = `Giá trị tối thiểu ${min}`): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        return control.value ? (control.value >= min ? null : { min: message }) : null;
    };
}

// Validator kiểm tra độ dài tối đa
export function maxValidator(max: number, message: string = `Giá trị tối đa ${max}`): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        return control.value ? (control.value <= max ? null : { max: message }) : null;
    };
}

// Validator kiểm tra định dạng URL
export function urlValidator(message: string = 'Url không hợp lệ'): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const urlPattern = /^(ftp|http|https):\/\/[^ "]+$/;
        if (!control.value || control.value.trim() === '') return null;
        return urlPattern.test(control.value) ? null : { url: message };
    };
}

// Validator kiểm tra số
export function numberValidator(message: string = 'Giá trị phải là số'): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        return !isNaN(control.value) ? null : { number: message };
    };
}

// Validator kiểm tra sự khớp nhau của hai trường (như password và confirm password)
export function matchValidator(controlName: string, matchingControlName: string, message: string = 'Các trường không khớp'): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
        const control = formGroup.get(controlName);
        const matchingControl = formGroup.get(matchingControlName);

        if (control && matchingControl && control.value !== matchingControl.value) {
            return { mismatch: message };
        }
        return null;
    };
}

// Validator kiểm tra định dạng số điện thoại
export function phoneValidator(message: string = 'Số điện thoại không hợp lệ'): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const phonePattern = /^(\+?\d{1,3}[- ]?)?(\d{1,4}[- ]?)*\d{1,4}$/;
        if (!control.value || control.value.trim() === '') return null;
        return phonePattern.test(control.value) ? null : { phone: message };
    };
}

export function sharePointUrlValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        if (control.value === null || control.value === undefined || control.value.trim() === '') return null;
        const urlRegex = /^(https?:\/\/)?([a-zA-Z0-9-]+)\.sharepoint\.com(\/.*)?$/;
        const valid = urlRegex.test(control.value.trim());
        return valid ? null : { invalidSharePointUrl: true };
    };
}
// Custom validator để kiểm tra khoảng cách giữa 2 ngày không vượt quá số lượng cho phép
export function dateRangeValidator(
    maxDifference: number, // Số lượng tối đa cho phép (36 tháng, 52 tuần, v.v.)
    type: 'year' | 'month' | 'week' | 'day', // Loại thời gian so sánh (năm, tháng, tuần, ngày)
    startKey: string, // Tên trường cho startTime
    endKey: string, // Tên trường cho endTime
): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
        const start = formGroup.get(startKey)?.value; // Lấy giá trị startTime từ form dựa trên key truyền vào
        const end = formGroup.get(endKey)?.value; // Lấy giá trị endTime từ form dựa trên key truyền vào

        if (!start || !end) {
            return null; // Nếu một trong hai giá trị không tồn tại, không kiểm tra và bỏ qua lỗi
        }

        let difference: number;
        let typeConvert: string;
        // Tính khoảng cách giữa start và end theo loại thời gian được chọn
        switch (type) {
            case 'year':
                difference = end.getFullYear() - start.getFullYear();
                typeConvert = 'năm';
                break;
            case 'month':
                difference = differenceInMonths(end, start);
                typeConvert = 'tháng';

                break;
            case 'week':
                difference = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 7));
                typeConvert = 'tuần';

                break;
            case 'day':
                difference = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
                typeConvert = 'ngày';

                break;
            default:
                difference = 0;
                typeConvert = '';
        }

        // Nếu khoảng cách lớn hơn số lượng cho phép (maxDifference), trả về lỗi
        if (difference > maxDifference) {
            return {
                maxDateRange: {
                    max: maxDifference, // Giá trị tối đa cho phép
                    actual: difference, // Khoảng cách thực tế
                    type: typeConvert,
                },
            };
        }

        return null; // Trả về null nếu không có lỗi
    };
}

export function nonEmptyArrayValidator(message?: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const array = control.value;
        return Array.isArray(array) && array.length > 0 ? null : { emptyArray: message ?? true };
    };
}

export function uniqueFieldValidator(fieldName: string, message?: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        if (!control.parent || !control.parent.parent) return null; // Kiểm tra tránh lỗi null

        const formArray = control.parent.parent as FormArray; // Ép kiểu FormArray
        if (!formArray.controls || formArray.controls.length === 0) return null;

        const values = (formArray.controls as FormGroup[]).map((group) => group.get(fieldName)?.value).filter((v) => v !== null && v !== undefined);

        const isDuplicate = values.filter((v) => v === control.value).length > 1;
        return isDuplicate ? { duplicateValue: message || `Giá trị ${fieldName} đã tồn tại!` } : null;
    };
}

export function focQuantityValidator(message: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const focQuantity = control.value;
        const quantity = control.parent?.get('quantity')?.value;

        if (focQuantity !== null && quantity !== null && focQuantity > quantity) {
            /*console.log('focQuantity ', focQuantity);
            console.log('quantity ', quantity);*/

            return { custom: message };
        }
        return null;
    };
}

export function focQuantityValidatorBoItem(message: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const focQuantity = control.value;
        const quantity = control.parent?.get('quantity')?.value;

        if (focQuantity !== null && quantity !== null && focQuantity > quantity) {
            /*console.log('focQuantity ', focQuantity);
            console.log('quantity ', quantity);*/

            return { custom: message };
        }
        return null;
    };
}

export function uniqueInternalReferenceValidator(): ValidatorFn {
    return (formArray: AbstractControl): ValidationErrors | null => {
        if (!formArray || !(formArray instanceof FormArray)) {
            return null;
        }

        const internalReferences = formArray.controls
            .map((control) => control.get('internalReference')?.value)
            .filter((value) => value !== null && value !== '');

        const duplicateValues: string[] = [];

        internalReferences.forEach((value, index) => {
            if (internalReferences.indexOf(value) !== index && !duplicateValues.includes(value)) {
                duplicateValues.push(value);
            }
        });

        if (duplicateValues.length > 0) {
            return { custom: `Đã tồn tại mã trùng: ${duplicateValues.join(', ')}` };
        }

        return null;
    };
}
