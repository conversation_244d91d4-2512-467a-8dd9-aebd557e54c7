@import './_var.scss';

* {
    box-sizing: border-box;
}

html {
    height: 100%;
    font-size: $scale;
}

body {
    font-family: var(--font-family);
    color: var(--text-color);
    background-color: var(--surface-ground);
    margin: 0;
    padding: 0;
    min-height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

.qc-upload-bttn {
    -webkit-text-size-adjust: 100%;
    position: relative;
    overflow: hidden;
    background: #fff;
    border: none;
    box-shadow:
        0.5px 1px 3px rgba(0, 0, 0, 0.12),
        0 1px 2px rgba(0, 0, 0, 0.24);
    text-align: center;
    text-shadow: none;
    transition: all 280ms ease;
    color: #212121;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    min-width: 14px;
    text-transform: capitalize;
    border-radius: 8px;
    padding: 4px 12px;
}

.qc-upload-input {
    position: absolute;
    top: 0;
    z-index: 1;
    width: 100%;
    opacity: 0;
    left: 0;
    cursor: pointer;
    font-size: 500px;
}

.qc-disabled {
    background-color: var(--background-disabled-qc);
    cursor: not-allowed !important;
    border-radius: 4px;
}

.p-th-sticky {
    position: sticky;
    background-color: #f8f9fa;
    z-index: 2;
}

.p-td-sticky {
    position: sticky;
    background-color: #ffffff;
    z-index: 2;
}

.attachment-item-name {
    vertical-align: middle;
    text-align: left !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 300px;
    display: inline-block;
}

.excel-color {
    color: #1d6f42;
    font-size: 14px !important;
}

.docx-color {
    color: #2b579a;
}

.p-dialog-mask {
    background-color: var(--maskbg);
}

.combobox::after {
    content: none !important; /* Loại bỏ pseudo-element ::after */
}

.combobox::before {
    content: none !important; /* Loại bỏ pseudo-element ::before */
}

.combobox > .p-overlaypanel-content {
    padding: 0 !important;
    position: relative;
}
.fullscreen {
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100vw !important;
    height: 100vh !important;
    padding: 1rem;
    background: #fff;
    z-index: 10000;
    overflow: auto;
}
// /* khi fullscreen (bạn dùng class .fullscreen) */
.fullscreen .p-overlaypanel {
    z-index: 11000 !important;
}
input[disabled],
button[disabled] {
    cursor: not-allowed;
}
.p-autocomplete-panel .p-autocomplete-items {
    max-height: 200px;
}
