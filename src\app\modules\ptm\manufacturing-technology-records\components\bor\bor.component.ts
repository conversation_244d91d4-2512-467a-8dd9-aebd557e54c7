import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'app-bor',
    standalone: true,
    imports: [CommonModule, FormsModule, InputTextareaModule, InfoShareComponent, FormCustomModule, PanelModule, TableModule, ButtonModule],
    templateUrl: './bor.component.html',
    styleUrls: ['./bor.component.scss'],
})
export class BorComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() onSubmit!: () => void; // Callback được truyền

    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();

    ngOnDestroy(): void {
        console.log('🧹 [BorComponent] Unmounted');
    }
    ngOnInit(): void {
        console.log('🧹 [BorComponent] Init');
    }
}
