<p-dialog
    [(visible)]="visible"
    [modal]="true"
    [closable]="false"
    [style]="{ width: '60vw' }"
    header="TRACK CHANGES"
>
    <p-table

        [value]="trackChanges"
        styleClass="p-datatable-gridlines"
    >
        <ng-template pTemplate="header">
            <tr>
                <th style="text-align: center">STT</th>
                <th style="text-align: center">Thao tác thay đổi</th>
                <th style="text-align: center"> Chi tiết cập nhật</th>
                <th style="text-align: center">Ng<PERSON><PERSON><PERSON> thực hiện</th>
                <th style="text-align: center">Thời điểm thực hiện</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item let-i="rowIndex">
            <tr>
                <td style="text-align: center">{{ i + 1 }}</td>
                <td style="text-align: center">{{ item.action }}</td>
                <td style="text-align: center">{{ item.detail }}</td>
                <td style="text-align: center">{{ item.user }}</td>
                <td style="text-align: center">{{ item.time }}</td>
            </tr>
        </ng-template>
    </p-table>

    <div class="tw-flex tw-justify-center tw-mt-4">
        <p-button label="Đóng" size="=large" (click)="handleClose()"></p-button>
    </div>
</p-dialog>
