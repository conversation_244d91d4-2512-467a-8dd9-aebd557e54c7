import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { Observable } from 'rxjs';

@Component({
    standalone: true,
    selector: 'app-tab-action-buttons',
    imports: [CommonModule, ButtonModule],
    template: `
        <div class="tw-flex tw-gap-4 tw-justify-start tw-mt-4">
            <p-button
                *ngIf="mode !== 'view'"
                label="Lưu"
                type="submit"
                [disabled]="isSaving | async"
                [loading]="isSaving | async"
                loadingIcon="pi pi-spinner pi-spin"
            >
            </p-button>
            <p-button
                *ngIf="canSubmitStatuses?.includes(status)"
                label="Gửi Preview"
                type="button"
                (click)="submit.emit()"
                [disabled]="(isSubmitting | async) || commonDisableSubmitStatuses?.includes(status) || (submitDisabled$ | async)"
                [loading]="isSubmitting | async"
                loadingIcon="pi pi-spinner pi-spin"
            >
            </p-button>
            <p-button
                *ngIf="canApproveStatuses?.includes(status)"
                label="Phê duyệt"
                type="button"
                (click)="approve.emit()"
                [disabled]="isApproving | async"
                [loading]="isApproving | async"
                loadingIcon="pi pi-spinner pi-spin"
            >
            </p-button>
        </div>
    `,
})
export class TabActionButtonsComponent {
    @Input() form!: FormGroup;
    @Input() mode: 'view' | 'create' | 'edit' = 'create';
    @Input() status!: number;
    @Input() canSubmitStatuses?: number[];
    @Input() canApproveStatuses?: number[];
    /** Các status chung luôn disable nút Gửi Preview */
    @Input() commonDisableSubmitStatuses?: number[];
    /** Observable chuyên biệt mỗi tab có thể truyền vào để disable */
    @Input() submitDisabled$?: Observable<boolean>;

    // THESE ARE AS OBSERVABLES NOW
    @Input() isSaving!: Observable<boolean>;
    @Input() isSubmitting!: Observable<boolean>;
    @Input() isApproving!: Observable<boolean>;

    @Output() submit = new EventEmitter<void>();
    @Output() approve = new EventEmitter<void>();
}
