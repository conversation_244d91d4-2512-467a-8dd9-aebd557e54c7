import { Compo<PERSON>, OnIni<PERSON>, Query<PERSON>ist, ViewChild, ViewChildren } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { ITEMS_STEP, STATUS_MAP } from 'src/app/models/constant/pms';
import { PanelModule } from 'primeng/panel';
import { TagModule } from 'primeng/tag';
import { ProDetailComponent } from '../pro-detail/pro-detail.component';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { InputValidationComponent } from 'src/app/shared/components/input-validation/input.validation.component';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { submitSpecificForm, TrimmedFormControl } from 'src/app/utils/form';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { ProjectPlanService } from 'src/app/services/ptm/project-plan/project-plan.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { Router } from '@angular/router';
import { ActionForTab } from 'src/app/models/interface/ptm/project-plan';

import { FormsModule, FormBuilder, FormControl, FormGroup, Validators, FormArray } from '@angular/forms';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';

import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';

@Component({
    selector: 'app-detail',
    templateUrl: 'detail.component.html',
    styleUrls: ['detail.component.scss'],
    standalone: true,
    imports: [
        SubHeaderComponent,
        WizardComponent,
        ComboboxNonRSQLComponent,
        TagModule,
        FormCustomModule,
        PanelModule,
        ButtonModule,
        RouterLink,
        InputTextModule,
        CalendarModule,
        ProDetailComponent,
        CommonModule,
        InputValidationComponent,
        FormsModule,
    ],
})
export class ProjectPlanDetailComponent implements OnInit {
    @ViewChild('customerFilter', { static: false })
    customerFilter!: ComboboxNonRSQLComponent;
    @ViewChild('projectNameFilter', { static: false })
    projectNameFilter!: ComboboxNonRSQLComponent;
    @ViewChild('picIdFilter', { static: false })
    picIdFilter!: ComboboxNonRSQLComponent;
    @ViewChildren(FormComponent)
    forms: QueryList<FormComponent>;
    @ViewChild(ProDetailComponent)
    proDetailComponentRef: ProDetailComponent;

    itemsHeader = [{ label: 'Kế hoạch dự án' }, { label: 'Danh sách kế hoạch dự án', url: '/ptm/project-plans' }, { label: 'Tạo mới' }];
    mode!: 'create' | 'view' | 'edit';
    formGroup: FormGroup;
    Info: string = '0 / 0';
    firstTimeFlags = new WeakMap();
    projectId: number = 0;
    activeIndex: number = 0;
    approvalStatus: number = 0;
    itemsStep = ITEMS_STEP;
    isViewOnly: boolean = false;

    constructor(
        private route: ActivatedRoute,
        private fb: FormBuilder,
        private pps: ProjectPlanService,
        private alertService: AlertService,
        private router: Router,
    ) {}

    ngOnInit() {
        // this.projectId = Number(this.route.snapshot.paramMap.get('id'));
        this.route.url.subscribe((segments) => {
            // segments là 1 mảng UrlSegment,
            this.mode = segments[0].path as 'create' | 'edit' | 'view';
        });

        this.route.paramMap.subscribe((params) => {
            const id = params.get('productId');
            if (id) {
                this.projectId = Number(id);

                if (this.mode === 'edit' || this.mode === 'view') {
                    this.pps.getProject(this.projectId).subscribe({
                        next: (projectDetail) => {
                            this.patchFormWithApiData(projectDetail);
                            if (this.mode === 'view') {
                                this.isViewOnly = true;
                            } else {
                                this.isViewOnly = false;
                            }
                        },
                    });
                }
            }
        });
        // if (this.mode === 'view') {
        //     this.formGroup.disable(); // ✅ disable toàn bộ form
        // }
        const lastLabel = this.mode === 'create' ? 'Tạo mới' : this.mode === 'edit' ? 'Chỉnh sửa' : 'Chi tiết';

        this.itemsHeader = [{ label: 'Kế hoạch dự án' }, { label: 'Danh sách kế hoạch dự án', url: '/ptm/project-plans' }, { label: lastLabel }];
        // this.initForm(this.role);
        this.formGroup = this.fb.group({
            projectName: ['', Validators.required],
            startDate: [null, Validators.required],
            endDate: [null, Validators.required],
            picOrPm: [null],
            productName: [null],
            vnptManPn: [null, Validators.required],
            completionRate: [''],
            completedTasks: [''],
            planDetails: new FormArrayCustom([]),
        });

        const cloneData = history.state?.cloneFrom;
        if (this.mode === 'create' && cloneData) {
            this.patchFormFromClone(cloneData);
            console.log('têttetet', cloneData);
        }
        this.approvalStatus = history.state?.approvalStatus;
        console.log('aaaaa', this.approvalStatus);
        // console.log('mk', this.projectId);
        // if (this.mode === 'edit') {
        //     this.pps.getProject(this.projectId).subscribe({
        //         next: (projectDetail) => {
        //             this.router.navigate(['/ptm/project-plans/edit', this.projectId]);
        //             this.patchFormWithApiData(projectDetail);
        //         },
        //     });
        // }
    }
    private patchFormFromClone(cloneData: any): void {
        this.formGroup.patchValue({
            projectName: `${cloneData.projectName} - Copy`,
            startDate: null,
            endDate: null,
            completionRate: '',
            productName: cloneData.productId,
            vnptManPn: cloneData.productId,
            picOrPm: cloneData.picId || null,
        });

        // if (cloneData.productName) {
        //     this.projectNameFilter?.filterOptions(cloneData.productName);
        // }
        // if (cloneData.manPN) {
        //     this.customerFilter?.filterOptions(cloneData.manPN);
        // }

        // const planDetailsFormArray = this.formGroup.get('planDetails') as FormArrayCustom;
        // planDetailsFormArray.clear();

        // if (Array.isArray(cloneData.planDetails)) {
        //     cloneData.planDetails.forEach((task: any) => {
        //         const clonedTask = new FormGroup({
        //             name: new FormControl(task.name || '', Validators.required),
        //             timeline: new FormControl(null, Validators.required), // Reset
        //             duration: new FormControl(null),
        //             predecessor: new FormControl(task.predecessor || ''),
        //             assignee: new FormControl(null),
        //             progress: new FormControl(0),
        //             status: new FormControl(null),
        //             note: new FormControl(task.note || ''),
        //             level: new FormControl(task.level || 1),
        //             displaySTT: new FormControl(task.displaySTT || '1'),
        //         });

        //         planDetailsFormArray.push(clonedTask);
        //     });
        // }
    }
    createPlanDetailRow(level = 1, displaySTT = '1', data: any = {}): FormGroup {
        return new FormGroupCustom(this.fb, {
            id: [data.id || null],
            name: [data.name || '', this.mode === 'edit' ? Validators.required : []],
            timeline: [data.timeline || null, this.mode === 'edit' ? Validators.required : []],
            duration: [data.duration || null],
            predecessor: [data.predecessor || ''],
            assignee: [data.assignee || null],
            progress: [data.progress || 0],
            status: [data.status ?? 2],
            note: [data.note || ''],
            level: [level],
            displaySTT: [displaySTT],
            action: [data.action ?? 1], // 0: NO_CHANGE
            rawData: [JSON.stringify(data)],
        });
    }

    onProductSelect(event: any): void {
        const selected = event.objects[0];
        console.log('Selected product:', selected);

        // Gán cho productName (nếu chưa có)
        if (selected.name) {
            this.formGroup.patchValue({
                productName: selected.id,
            });
            this.projectNameFilter.filterOptions(selected.name);
        }
        if (selected.vnptManPn) {
            this.formGroup.patchValue({
                vnptManPn: selected.id,
            });
            this.customerFilter.filterOptions(selected.vnptManPn);
        }
    }

    // Hàm tạo một task mới có validate timeline
    createTaskForm(): FormGroup {
        return this.fb.group({
            name: ['', Validators.required],
            timeline: [null, Validators.required], // ✅ timeline bắt buộc
            duration: [null],
            predecessor: [''],
            assignee: [null],
            progress: [0],
            status: [null],
            note: [''],
        });
    }

    // initForm(project)
    submitForm(formPL: string): void {
        this.proDetailComponentRef?.validatePredecessor();
        this.formGroup.markAllAsTouched();
        submitSpecificForm(this.forms, formPL); // Gọi hàm util

        if (this.formGroup.invalid) return;

        const formValue = this.formGroup.value;

        const payload: any = {
            name: formValue.projectName,
            startDate: new Date(formValue.startDate).getTime(),
            endDate: new Date(formValue.endDate).getTime(),
            productId: formValue.productName || 78,
            status: 1,
            picId: formValue.picOrPm || 0,
        };

        if (this.mode === 'create') {
            this.handleCreateProject(payload);
        } else if (this.mode === 'edit') {
            payload.tasks = this.mapTasks(formValue.planDetails);
            this.handleUpdateProject(this.projectId, payload);
        }
    }

    handleUpdateProject(projectId: number, payload: any): void {
        this.pps.updateProjectPlan(projectId, payload).subscribe({
            next: () => {
                this.alertService.success('Thành công', 'Cập nhật kế hoạch thành công');
                this.ngOnInit();
            },
            error: (err) => {
                console.error('Lỗi cập nhật kế hoạch:', err);
                this.alertService.error('Lỗi', 'Không thể cập nhật kế hoạch');
            },
        });
    }

    private mapTasks(tasks: any[]): any[] {
        let currentBaseLevel = 0;
        return tasks.map((task: any) => {
            const [startDate, endDate] = task.timeline || [];
            const displaySTT = task.displaySTT || '1';

            const levelDepth = displaySTT.split('.').length;

            // Nếu là cấp 1 (ví dụ: "1", "2", "3") => reset lại level tính từ 1
            if (!displaySTT.includes('.')) {
                currentBaseLevel = 1;
            }

            let level = currentBaseLevel + (levelDepth - 1);
            let action = task.action ?? ActionForTab.UPDATE;
            const mappedTask: any = {
                multilevelNumbering: task.displaySTT,
                level: level,
                name: task.name,
                startDate: startDate ? new Date(startDate).getTime() : null,
                endDate: endDate ? new Date(endDate).getTime() : null,
                duration: task.duration,
                status: task.status ?? 2,
                predecessor: task.predecessor,
                progressPercent: task.progress,
                assigneeId: task.assignee,
                assigneeName: String(task.assignee),
                note: task.note,
                action, // action default nếu không có
            };
            if (task.id) {
                mappedTask.id = task.id;
            }
            return mappedTask;
        });
    }
    handleCreateProject(payload) {
        this.pps.createProjectPlan(payload).subscribe({
            next: (res) => {
                this.alertService.success('Thành công', 'Tạo kế hoạch thành công');
                this.mode = 'edit';
                // this.projectId = res.id;
                this.projectId = res.id;
                console.log('ânn', this.projectId);
                this.router.navigate(['/ptm/project-plans/edit', res.id]);
                return this.pps.getProject(res.id).subscribe({
                    next: (projectDetail) => {
                        this.patchFormWithApiData(projectDetail);
                    },
                });
            },
        });
    }
    private patchFormWithApiData(project: any): void {
        if (!project) return;
        console.log('bbbbb', project.tasks.length);

        this.formGroup.patchValue({
            projectName: project.name,
            startDate: new Date(project.startDate),
            endDate: new Date(project.endDate),
            productName: project.productId,
            vnptManPn: project.productId,
            // completionRate: project.rate,
            picOrPm: project.picId,
        });
        this.Info = project.rate;
        if (project.productName) {
            this.projectNameFilter.filterOptions(project.productName);
        }
        if (project.picId) {
            this.picIdFilter.filterOptions(project.picId);
        }

        const statusLabel = STATUS_MAP[this.approvalStatus];
        const idx = ITEMS_STEP.findIndex((item) => item.name === statusLabel);
        this.activeIndex = idx !== -1 ? idx : 0;

        this.customerFilter.filterOptions(project.manPN);

        const taskFormArray = this.formGroup.get('planDetails') as FormArrayCustom;
        taskFormArray.clear();
        console.log('tasks from API:', project.tasks);
        console.log('tasks form group', this.formGroup.value);
        if (Array.isArray(project.tasks) && project.tasks.length > 0) {
            for (const task of project.tasks) {
                const formGroup = this.createPlanDetailRow(task.level, task.multilevelNumbering, {
                    id: task.id,
                    name: task.name || '',
                    timeline: [new Date(task.startDate), new Date(task.endDate)],
                    duration: task.duration || null,
                    predecessor: task.predecessor || '',
                    assignee: task.assigneeId || null,
                    progress: task.progressPercent || 0,
                    status: task.status ? task.status : 2,
                    note: task.note || '',
                    action: task.action ?? 0, // ✅ mặc định là NO_CHANGE
                });
                console.log('formGroup11', formGroup);

                formGroup.addControl('id', new FormControl(task.id));
                taskFormArray.push(formGroup);
            }
        } else {
            const defaultTask = this.createPlanDetailRow(1, '1');
            taskFormArray.push(defaultTask);
        }
        console.log('kkkkk', taskFormArray);
    }
    get planDetailsArray(): FormArrayCustom {
        return this.formGroup.get('planDetails') as FormArrayCustom;
    }
    handlePanelShow(ref: any) {
        if (!this.firstTimeFlags.has(ref)) {
            this.firstTimeFlags.set(ref, true);
            return;
        }
        ref.fetchOptions(null);
    }
}
