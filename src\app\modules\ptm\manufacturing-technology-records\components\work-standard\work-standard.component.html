<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share
            [title]="title"
            (changeValueApprover)="handleApproverChange($event)"
            [name]="nameTab"
            [detailInfo]="detailPfmea"
        ></app-info-share>
    </div>
    <div class="p-fluid tw-space-y-2">
        <div class="tw-flex tw-justify-between tw-border-b-2">
            <label class="label-tab-cnsx">Chi tiết Work Standard</label>
            <div class="tw-flex tw-space-x-2">
                <p-button label="Track Changes" size="small"></p-button>
                <p-button label="Xuất excel" size="small"></p-button>
            </div>
        </div>
    </div>
    <div> SMTTOP</div>
    <div class="tw-flex tw-flex-col tw-p-4 tw-justify-center tw-border-[1px] tw-border-solid tw-border-gray-300 tw-rounded-md">
        <app-form-item label="" class="tw-w-full">
            <app-form #form [formGroup]="formGroup" layout="vertical">
                <div formArrayName="workStandards">
                    <p-table styleClass="p-datatable-gridlines" [value]="listWorkStandard.controls">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 9rem ;text-align: center ">STT</th>
                                <th *ngFor="let col of columns"
                                    [style.minWidth]="col.minWidth"
                                    [style.text-align]="'center'"
                                    [style.maxWidth]="col.maxWidth">
                                    {{ col.header }}
                                </th>
                                <th style=" text-align: center">Thao tác</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr [formGroupName]="rowIndex">
                                <td>
                                    <app-form-item label=""> {{ rowIndex + 1 }}</app-form-item>
                                </td>

                                <td>
                                    <app-form-item label="">
                                        {{ item.get('oc').value }}
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        {{ item.get('od').value }}
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        {{ item.get('nextOc').value }}
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="">
                                        {{ item.get('nextOc').value }}
                                    </app-form-item>
                                </td>

                                <td class="tw-flex tw-justify-center">
                                    <button pButton type="button" (click)="openPanel(rowIndex, op, $event)"
                                            label="Thêm/sửa chi tiết"></button>

                                    <!-- Overlay Panel gắn vào đây -->
                                    <p-overlayPanel #op [dismissable]="false" [showCloseIcon]="true"
                                                    [style]="{ width: '90vw' }" appendTo="body">
                                        <form [formGroup]="item">
                                            <div formArrayName="workStandardDetails">
                                                <p-table styleClass="p-datatable-gridlines"
                                                         [value]="item.get('workStandardDetails').controls">
                                                    <!-- HEADER -->
                                                    <ng-template pTemplate="header">
                                                        <tr>
                                                            <th rowspan="2">STT</th>

                                                            <!-- Hàng 1: Nhóm cột -->
                                                            <ng-container *ngFor="let group of getColumnGroups()">
                                                                <th style="text-align: center"
                                                                    [attr.colspan]="group.colSpan" *ngIf="group.name">
                                                                    {{ group.name }}
                                                                </th>
                                                                <th style="text-align: center" *ngIf="!group.name"
                                                                    rowspan="2">
                                                                    {{ group.headers[0].header }}
                                                                </th>
                                                            </ng-container>
                                                            <th style="text-align: center" rowspan="2">
                                                                <button pButton icon="pi pi-plus"
                                                                        (click)="addRow(rowIndex)"
                                                                        class="p-button-text"></button>
                                                            </th>
                                                        </tr>

                                                        <tr>
                                                            <!-- Hàng 2: Header con nếu có nhóm -->
                                                            <ng-container *ngFor="let group of getColumnGroups()">
                                                                <ng-container *ngIf="group.name">
                                                                    <th style="text-align: center"
                                                                        *ngFor="let col of group.headers">{{ col.header }}
                                                                    </th>
                                                                </ng-container>
                                                            </ng-container>
                                                        </tr>
                                                    </ng-template>

                                                    <!-- BODY -->
                                                    <ng-template pTemplate="body" let-row let-i="rowIndex">
                                                        <tr [formGroupName]="i">
                                                            <td>{{ i + 1 }}</td>
                                                            <td><input pInputText
                                                                       formControlName="operationDescription" />
                                                            </td>
                                                            <td><input pInputText formControlName="in" /></td>
                                                            <td><input pInputText formControlName="labor" /></td>
                                                            <td><input pInputText formControlName="machine" /></td>
                                                            <td><input pInputText formControlName="out" /></td>
                                                            <td>
                                                                <p-dropdown [options]="[]"
                                                                            formControlName="equip"
                                                                            optionLabel="label"
                                                                            optionValue="value"
                                                                            appendTo="body" />
                                                            </td>
                                                            <td><input pInputText formControlName="equipQty" /></td>
                                                            <td>
                                                                <p-dropdown [options]="[]"
                                                                            formControlName="consumablePn"
                                                                            optionLabel="label"
                                                                            optionValue="value"
                                                                            appendTo="body"
                                                                            filter="true" />
                                                            </td>
                                                            <td>{{ row.get('consumableDesc')?.value || '-' }}</td>
                                                            <td><input pInputText formControlName="consumableQty" />
                                                            </td>
                                                            <td><input pInputText formControlName="unit" /></td>
                                                            <td><input pInputText formControlName="laborAllowance" />
                                                            </td>
                                                            <td><input pInputText formControlName="laborUtil" /></td>
                                                            <td><input pInputText formControlName="machineUtil" /></td>
                                                            <td><input pInputText formControlName="btpTram" /></td>
                                                            <td>{{ calculateLaborTime(rowIndex, i) }}</td>
                                                            <td>{{ calculateMachineTime(rowIndex, i) }}</td>
                                                            <td>
                                                                <button pButton icon="pi pi-minus"
                                                                        (click)="removeRow(rowIndex,i)"
                                                                        class="p-button-text text-red-500"></button>
                                                            </td>
                                                        </tr>
                                                    </ng-template>
                                                </p-table>
                                            </div>
                                        </form>
                                        <div class="tw-flex tw-justify-center tw-mt-2">
                                            <button pButton label="Lưu" class="tw-mr-2" (click)="save()"></button>
                                            <button pButton label="Đóng" class="tw-mr-2" (click)="op.hide()"></button>
                                        </div>
                                    </p-overlayPanel>
                                </td>

                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </app-form>
        </app-form-item>
        <div class="tw-flex tw-justify-between tw-border-b-2">
            <div>
                <p-button label="Track Changes" size="small"></p-button>
            </div>
            <div class="tw-flex tw-space-x-2">

                <p-button label="Xuất excel" size="small"></p-button>
            </div>
        </div>
    </div>

</ng-container>

