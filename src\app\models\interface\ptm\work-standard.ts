export interface WorkStandardDetailDto {
    id: number;
    workStandardId: number;
    od: string;
    inTime: number;
    laborTime: number;
    machineTime: number;
    outTime: number;
    equipId: number;
    equipName: string | null;
    equipQty: number;
    consumableMaterialPnId: number;
    consumableMaterialPnName: string;
    consumableDescription: string;
    qty: number;
    unit: string;
}

export interface WorkStandard {
    id: number;
    instructionId: number;
    processFlowId: number;
    oc: string;
    nextOc: string;
    od: string;
    pfmeaId: number;
    line: number;
    laborAllowance: number;
    laborUtilization: number;
    machineUtilization: number;
    btp: number;
    laborTime: number;
    machine: number;
    workStandardDetailDtos: WorkStandardDetailDto[];
}


