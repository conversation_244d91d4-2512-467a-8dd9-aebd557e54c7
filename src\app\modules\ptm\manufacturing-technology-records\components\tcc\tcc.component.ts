import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';

@Component({
    selector: 'app-tcc',
    templateUrl: './tcc.component.html',
    standalone: true,
    styleUrls: ['./tcc.component.scss'],
    imports: [CommonModule, FormsModule, InputTextareaModule, InfoShareComponent, ButtonModule, TableModule, DropdownModule],
})
export class TccComponent {
    @Input() content: string = '';
    @Input() title: string = '';
    collapsed = false;

    form = {
        soCa: null,
        thoiGianLamViec: null,
        donViLamViec: 'giờ',
        giaiLao: null,
        anTrua: null,
        anPhu: null,
        thoiGianSanXuat: null,
        donViSanXuat: 'giây',
    };

    timeUnits = [
        { label: 'giây', value: 'giây' },
        { label: 'phút', value: 'phút' },
        { label: 'giờ', value: 'giờ' },
    ];

    toggleCollapse() {
        this.collapsed = !this.collapsed;
    }
}
