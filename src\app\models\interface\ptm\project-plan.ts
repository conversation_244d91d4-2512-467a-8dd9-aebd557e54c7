import { BaseEntity } from '../../BaseEntity';
import { TemplateRef } from '@angular/core';

export interface CreateProject extends BaseEntity {
    name: string;
    startDate: number;
    endDate: number;
    productId: number;
    status: number;
    picId: number;
}

export interface UpdateProject {
    name: string;
    startDate: number;
    endDate: number;
    productId: number;
    status: number;
    picId: number;
    tasks?: any[];
}

export interface ProjectDetail {
    id: number;
}

export interface Version {
    id?: number;
    version: string;
    lastUpdate: Date;
    statusName: 'Approved' | 'Rejected' | 'Draft' | string;
    selected?: boolean;
    note?: string;
    lifecycleStage?: number;
    status?: number;
    isCurrent?: unknown;
}

export interface SendApprovalRequest {
    projectId: number;
    email: string;
    note: string;
}

export enum ActionForTab {
    NO_CHANGE = 0,
    CREATE = 1,
    UPDATE = 2,
    DELETE = 3,
}

export interface ApprovalRequest {
    projectId: number;
    type: number;
    note: string;
}

export interface Column {
    field?: string;
    sort?: string;
    typeSort?: 'desc' | 'asc';
    header: string;
    hide?: boolean;
    visible?: boolean;
    style?: Record<string, unknown>;
    body?: TemplateRef<unknown> | string;
    bodyWrapper?: TemplateRef<unknown> | string;
    rowspan?: number;
    colspan?: number;
    default?: boolean;
    index?: number;
    format?: string;
    type?: 'date' | 'number' | 'link' | 'newTab' | 'currency';
    fixed?: 'right' | 'left';
    url?: string;
    fallBackValue?: unknown;
    group?: string;
}

export interface ProjectPlan extends BaseEntity {
    projectName: string;
    productName: string;
    vnptManPn: string;
    rate: string;
    milestoneMultilevelNumbering: string;
    approvalStatus?: number;
    completeRatio: number;
    doneTask: number;
    endDate: number;
    startDate: number;
    totalTask: number;
    updated: number;
}
