import { BaseEntity } from '../BaseEntity';

export interface ProcessFlow extends BaseEntity {
    versionId: number;
    phase: number;
    reviewerIds: number[];
    listProcessFlow: ProcessFlowDetail[];
    processFlows: object;
}

export interface ProcessFlowDetail extends BaseEntity {
    id: number;
    processFlowId: number;
    step: number;
    oc: string;
    nextOc: string;
    symbol: string;
    operationDescription: string;
    productAndProcessCharacteristics: string;
    online: boolean;
    offline: boolean;
}
export interface Routing extends BaseEntity {
    listRouting: RoutingDetail[];
}
// SOP-BASE

export interface SectionEntrySopBase {
    id: number;
    STT: number;
    OC: string;
    mainOperationBlock: string;
    operationDescription: string;
    focusLevel: number;
    workArea: string;
    equipmentName: string;
    equipmentQty: number;
    materialCode: string;
    materialName: string;
    materialQty: string;
    workStationNotes?: string;
    details?: string;
    basicOperationTime?: string;
}

export interface SectionSobBase {
    /** <PERSON><PERSON> kh<PERSON>i, ví dụ "SMTTOP", "SMTBOT", ... */
    code: string;
    /** <PERSON>h sách dòng của khối */
    entries: SectionEntrySopBase[];
}

export interface RoutingDetail extends BaseEntity {
    id: number;
    routingId: number;
    step: number;
    operationDescription: string;
    operationTime: number;
    operationUnit: string;
    operationQuantity: number;
    operationCost: number;
    operationResource: string;
}

export interface ManBom extends BaseEntity {
    listManBom: ManBomDetail[];
}

export interface ManBomDetail extends BaseEntity {
    id: number;
    manBomId: number;
    description: number;
    unit: string;
    process: number;
    quantity: string;
    reference: string;
    materialType: number;
    attritionRate: string;
    note: string;
}

export interface ProductionInstruction {
    id: number;
    versionId: number;
    phase: number;
    status: number;
    versionName: string;
    created: number;
    updated: number;
    createdBy: string;
    lifecycleStage: number;
}
export interface Task {
    id?: string;
    name: string;
    timeLine: DateRange;
    duration: number;
    predecessor?: string;
    assignee: IAssignee;
    progress: number;
    status: TaskStatus;
    note?: string;
}
export interface DateRange {
    start: Date | null;
    end: Date | null;
}
export interface IAssignee {
    id: string;
    name: string;
    email?: string;
    avatar?: string;
}

export enum TaskStatus {
    COMPLETED = 1, // Đã hoàn thành
    NOT_COMPLETED = 2, // Chưa hoàn thành
    PENDING = 3, // Đang chờ xử lý
    CANCELLED = 4, // Đã hủy
}

export interface PFMEA  extends BaseEntity {
    listPFMEA: PFMEADetail[];
}

export interface PFMEADetail extends BaseEntity {
    id: number;
    line: number;
    oc: string;
    operationDescription: string;
    standard: string;   // Tiêu chuẩn/Yêu cầu/Mục tiêu

}

export interface WorkStandard  extends BaseEntity {
    listWorkStandard: WorkStandardDetail[];
}

export interface WorkStandardDetail extends BaseEntity {
    id: number;
    line: number;
    oc: string;
    operationDescription: string;
    standard: string;   // Tiêu chuẩn/Yêu cầu/Mục tiêu

}

