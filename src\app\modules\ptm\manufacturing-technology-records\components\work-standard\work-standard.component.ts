import { Component, EventEmitter, inject, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroupCustom } from '../../../../../shared/form-module/from-group.custom';
import { FormArray, FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { PRODUCT_PROCESS_TYPE } from '../../../../../models/constant/ptm';
import { FormComponent } from '../../../../../shared/form-module/form-base/form.component';
import { FormArrayCustom } from '../../../../../shared/form-module/from-array.custom';
import { PFMEA, PFMEADetail, ProcessFlow } from '../../../../../models/interface/ptm';
import { CommonModule } from '@angular/common';
import { InputTextModule } from 'primeng/inputtext';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from '../../../../../shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { numberValidator } from '../../../../../utils/validator';
import { WorkStandard, WorkStandardDetailDto } from '../../../../../models/interface/ptm/work-standard';
import { PfmeaService } from '../../../../../services/ptm/pfmea/pfmea.service';
import { WorkStandardService } from '../../../../../services/ptm/work-standard/work-standard.service';

@Component({
    selector: 'app-work-standard',
    templateUrl: './work-standard.component.html',
    styleUrls: ['./work-standard.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        InfoShareComponent,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        DropdownModule,
        CheckboxModule,
        OverlayPanelModule,
        CalendarModule,
    ],
})
export class WorkStandardComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() productInstructionId: number;
    @Input() currentProduct: any;

    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();
    @Output() changed = new EventEmitter<string>();

    pfmeaService = inject(PfmeaService);
    workStandardService = inject(WorkStandardService);

    columnErrorTable = [
        { field: 'operationDescription', header: 'Operation Details' },

        { field: 'in', header: 'In (s)', group: 'Normal Time' },
        { field: 'labor', header: 'Labor (s)', group: 'Normal Time' },
        { field: 'machine', header: 'Machine (s)', group: 'Normal Time' },
        { field: 'out', header: 'Out (s)', group: 'Normal Time' },

        { field: 'equip', header: 'Equip', group: 'Sub' },
        { field: 'qty1', header: 'Qty', group: 'Sub' },
        { field: 'materialPN', header: 'Consumable Material PN', group: 'Sub' },
        { field: 'materialDesc', header: 'Consumable description', group: 'Sub' },
        { field: 'qty2', header: 'Qty', group: 'Sub' },
        { field: 'unit', header: 'Unit', group: 'Sub' },

        { field: 'laborAllowance', header: 'Labor Allowance (%)', group: 'Time Rate' },
        { field: 'laborUtil', header: 'Labor Utilization (%)', group: 'Time Rate' },
        { field: 'machineUtil', header: 'Machine Utilization (%)', group: 'Time Rate' },
        { field: 'btpTram', header: 'BTP/Trạm', group: 'Time Rate' },

        { field: 'laborTime', header: 'Labor Time (s)', group: 'Standard Time' },
        { field: 'machineTime', header: 'Machine (s)', group: 'Standard Time' },
    ];

    columns = [
        { field: 'oc', header: 'OC', minWidth: '9rem' },
        { field: 'step', header: 'Next OC', minWidth: '9rem' },
        { field: 'nextOC', header: 'Operation Description', minWidth: '9rem' },
        { field: 'symbol', header: 'Công đoạn', minWidth: '9rem' },
    ];

    workStandards = [
        {
            id: 3,
            instructionId: 3,
            processFlowId: 17,
            oc: 'OC1',
            nextOc: 'OC2',
            od: 'Mô tả quy trình 1',
            pfmeaId: 17,
            line: 1,
            laborAllowance: 1.15,
            laborUtilization: 0.85,
            machineUtilization: 0.92,
            btp: 100,
            laborTime: 25.5,
            machine: 12.3,
            workStandardDetailDtos: [
                {
                    id: 1,
                    workStandardId: 3,
                    od: 'OD-001',
                    inTime: 0,
                    laborTime: 8.5,
                    machineTime: 4,
                    outTime: 12.5,
                    equipId: 101,
                    equipName: null,
                    equipQty: 1,
                    consumableMaterialPnId: 301,
                    consumableMaterialPnName: 'Băng dính',
                    consumableDescription: 'Băng dính nhiệt độ cao',
                    qty: 2,
                    unit: 'cuộn',
                },
                {
                    id: 2,
                    workStandardId: 3,
                    od: 'OD-002',
                    inTime: 12.5,
                    laborTime: 10,
                    machineTime: 5,
                    outTime: 22.5,
                    equipId: 102,
                    equipName: null,
                    equipQty: 2,
                    consumableMaterialPnId: 302,
                    consumableMaterialPnName: 'Dầu bôi trơn',
                    consumableDescription: 'Dầu bôi trơn công nghiệp',
                    qty: 1,
                    unit: 'lít',
                },
                {
                    id: 4,
                    workStandardId: 3,
                    od: 'OD-001',
                    inTime: 0,
                    laborTime: 8.5,
                    machineTime: 4,
                    outTime: 12.5,
                    equipId: 101,
                    equipName: null,
                    equipQty: 1,
                    consumableMaterialPnId: 301,
                    consumableMaterialPnName: 'Băng keo kiệt',
                    consumableDescription: 'Băng dính nhiệt độ cao',
                    qty: 2,
                    unit: 'cuộn',
                },
                {
                    id: 5,
                    workStandardId: 3,
                    od: 'OD-002',
                    inTime: 12.5,
                    laborTime: 10,
                    machineTime: 5,
                    outTime: 22.5,
                    equipId: 102,
                    equipName: null,
                    equipQty: 2,
                    consumableMaterialPnId: 302,
                    consumableMaterialPnName: 'Dầu bôi bác',
                    consumableDescription: 'Dầu bôi trơn công nghiệp',
                    qty: 1,
                    unit: 'lít',
                },
            ],
        },
        {
            id: 4,
            instructionId: 3,
            processFlowId: 21,
            oc: 'OC1',
            nextOc: 'OC2',
            od: 'Mô tả quy trình 1',
            pfmeaId: 18,
            line: 1,
            laborAllowance: 1.1,
            laborUtilization: 0.8,
            machineUtilization: 0.88,
            btp: 120,
            laborTime: 30,
            machine: 15,
            workStandardDetailDtos: [
                {
                    id: 3,
                    workStandardId: 4,
                    od: 'OD-003',
                    inTime: 0,
                    laborTime: 15,
                    machineTime: 6,
                    outTime: 21,
                    equipId: 103,
                    equipName: null,
                    equipQty: 1,
                    consumableMaterialPnId: 303,
                    consumableMaterialPnName: 'Giấy nhám',
                    consumableDescription: 'Giấy nhám P800',
                    qty: 10,
                    unit: 'tờ',
                },
                {
                    id: 6,
                    workStandardId: 4,
                    od: 'OD-003',
                    inTime: 0,
                    laborTime: 15,
                    machineTime: 6,
                    outTime: 21,
                    equipId: 103,
                    equipName: null,
                    equipQty: 1,
                    consumableMaterialPnId: 303,
                    consumableMaterialPnName: 'Giấy chùi',
                    consumableDescription: 'Giấy nhám P800',
                    qty: 10,
                    unit: 'tờ',
                },
            ],
        },
    ];

    formGroup: FormGroup;

    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    clonedPfmeaErrors: { [index: number]: any[] } = {};
    nameTab: string;
    detailPfmea: any;

    get listWorkStandard(): FormArray {
        return this.formGroup.get('workStandards') as FormArray;
    }

    constructor(private fb: FormBuilder) {
        this.formGroup = this.fb.group({
            instructionId: [''],
            status: [''],
            type: [''],
            reviewers: [''],
            workStandards: this.fb.array([]),
        });
    }

    ngOnInit(): void {
        this.formGroup.get('instructionId').setValue(this.productInstructionId);
        this.nameTab = 'WS-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
        // this.setWorkStandardsToForm(this.workStandards);
        this.getDataPfmeaUpdate(3);
    }

    setWorkStandardsToForm(workStandards: any[]): void {
        this.listWorkStandard.clear();

        workStandards.forEach((ws) => {
            this.listWorkStandard.push(this.createWorkStandardGroup(ws));
        });
    }

    getDataPfmeaUpdate(id) {
        console.log('chạy init');
        this.workStandardService.getWorkStandard(id).subscribe({
            next: (res) => {
                const dataInstructionInfo = { instructionInfo: res.instructionInfo };
                this.detailPfmea = { ...dataInstructionInfo };
                const reviewers = res?.instructionInfo?.reviewers?.map((r) => r.id).join(',');
                this.formGroup.get('reviewers').setValue(reviewers);
                this.setWorkStandardsToForm(res.workStandards);
            },
            error: (err) => {
                console.log(err);
            },
        });
    }

    createWorkStandardGroup(data: WorkStandard): FormGroup {
        return this.fb.group({
            id: [data.id],
            instructionId: [data.instructionId],
            processFlowId: [data.processFlowId],
            oc: [data.oc],
            nextOc: [data.nextOc],
            od: [data.od],
            pfmeaId: [data.pfmeaId],
            line: [data.line],
            laborAllowance: [data.laborAllowance],
            laborUtilization: [data.laborUtilization],
            machineUtilization: [data.machineUtilization],
            btp: [data.btp],
            laborTime: [data.laborTime],
            machine: [data.machine],
            workStandardDetails: this.fb.array((data.workStandardDetailDtos || []).map((d) => this.createWorkStandardDetailGroup(d))),
        });
    }

    createWorkStandardDetailGroup(detail: WorkStandardDetailDto): FormGroup {
        return this.fb.group({
            operationDescription: [detail.od],
            in: [detail.inTime],
            labor: [detail.laborTime],
            machine: [detail.machineTime],
            out: [detail.outTime],
            equip: [detail.equipId],
            equipQty: [detail.equipQty],
            consumablePn: [detail.consumableMaterialPnId],
            consumableDesc: [detail.consumableDescription],
            consumableQty: [detail.qty],
            unit: [detail.unit],
            laborAllowance: [''], // placeholder for extended calculation if needed
            laborUtil: [''],
            machineUtil: [''],
            btpTram: [''],
        });
    }

    createWorkStandardDetailRow(): FormGroup {
        return this.fb.group({
            operationDescription: [''],
            in: [''],
            labor: [''],
            machine: [''],
            out: [''],
            equip: [null],
            equipQty: [''],
            consumablePn: [null],
            consumableDesc: [''],
            consumableQty: [''],
            unit: [''],
            laborAllowance: [''],
            laborUtil: [''],
            machineUtil: [''],
            btpTram: [''],
        });
    }

    initRowErrorTable(): FormGroup {
        return new FormGroupCustom(this.fb, {
            // Operation Details
            operationDescription: [''],

            // Normal Time
            in: ['', [Validators.required, numberValidator()]],
            labor: [''],
            machine: [''],
            out: [''],

            // Equip
            equip: [null],
            equipQty: [''],

            // Consumable
            consumablePn: [null],
            consumableDesc: [''], // dùng để hiển thị mô tả (readonly/label)
            consumableQty: [''],
            unit: [''],

            // Time Rate (dữ liệu chung nhưng bạn vẫn cần đặt trong từng row để form hoạt động độc lập)
            laborAllowance: ['6'], // mặc định 6%
            laborUtil: [''],
            machineUtil: [''],
            btpTram: [''],

            oc: [''], // nếu bạn cần dùng để render STT OC.x
        });
    }

    initFormContact(items: PFMEADetail[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    oc: [item?.oc],
                    line: [item?.line],
                    operationDescription: [item?.operationDescription],
                    standard: [item?.standard],
                }),
        );
    }

    ngOnDestroy(): void {
        console.log('🧹 [ProcessFlowComponent] Unmounted');
    }

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    sortProcessFlow() {}

    onSubmit(value: ProcessFlow): void {
        console.log(value, value.listProcessFlow);
    }

    openPanel(index: number, overlay: OverlayPanel, event: MouseEvent): void {
        const errors = this.listWorkStandard.at(index).get('workStandardDetails') as FormArray;
        this.clonedPfmeaErrors[index] = errors.value.map((e) => ({ ...e })); // deep copy
        setTimeout(() => {
            overlay.toggle(event); // tùy chọn, có thể bỏ nếu dùng setTimeout
        }, 0);
    }

    addRow(pfmeaIndex: number): void {
        const errors = this.listWorkStandard.at(pfmeaIndex).get('workStandardDetails') as FormArray;
        errors.push(this.initRowErrorTable());
    }

    removeRow(pfmeaIndex: number, errorIndex: number): void {
        const pfmeaErrors = this.listWorkStandard.at(pfmeaIndex).get('workStandardDetails') as FormArray;
        if (pfmeaErrors && pfmeaErrors.length > errorIndex) {
            setTimeout(() => {
                pfmeaErrors.removeAt(errorIndex);
            }, 0);
        }
    }

    getData() {
        return this.formGroup.getRawValue();
    }

    validateProcess() {
        // console.log('validateProcess', this.formGroup.valid);
        return this.formGroup.valid;
    }

    errorProcess() {
        console.log('errorProcess');
    }

    handleSubmit() {
        // Gọi callback nếu có

        // Emit event nếu cha lắng nghe
        this.submitted.emit({
            content: this.content,
            config: this.config,
            items: this.items,
        });
    }

    calculateRPN(sev?: any, occ?: any, det?: any): number {
        const s = parseInt(sev, 10);
        const o = parseInt(occ, 10);
        const d = parseInt(det, 10);
        if (!isNaN(s) && !isNaN(o) && !isNaN(d)) {
            return Math.max(1, Math.round(s * o * d));
        }
        return 0;
    }

    save() {}

    getColumnGroups() {
        const grouped = new Map<string | null, any[]>();

        for (const col of this.columnErrorTable) {
            const key = col.group || null;
            if (!grouped.has(key)) grouped.set(key, []);
            grouped.get(key).push(col);
        }

        return Array.from(grouped.entries()).map(([name, headers]) => ({
            name,
            headers,
            colSpan: headers.length,
        }));
    }

    getSTTForOC(index: number): string {
        const oc = this.formGroup.get('listPFMEA')?.get([index])?.get('oc')?.value;
        return `${oc || 'OC'}.${index + 1}`;
    }

    calculateLaborTime(workStandardIndex: number, workStandardDetailIndex: number): string {
        const rows = this.listWorkStandard.at(workStandardIndex).get('workStandardDetails') as FormArray;
        const rowIndex = rows.at(workStandardDetailIndex);
        const labor = +rowIndex.get('labor')?.value || 0;
        const allowance = +rowIndex.get('laborAllowance')?.value || 0;
        const util = +rowIndex.get('laborUtil')?.value || 0;

        const result = (labor * (1 + allowance / 100)) / (util || 1);
        return isNaN(result) ? '-' : result.toFixed(2);
    }

    calculateMachineTime(workStandardIndex: number, workStandardDetailIndex: number): string {
        const rows = this.listWorkStandard.at(workStandardIndex).get('workStandardDetails') as FormArray;
        const rowIndex = rows.at(workStandardDetailIndex);
        const machine = +rowIndex.get('machine')?.value || 0;
        const util = +rowIndex.get('machineUtil')?.value || 0;

        const result = machine / (util || 1);
        return isNaN(result) ? '-' : result.toFixed(2);
    }

    handleApproverChange(event: any) {
        const reviewers = event.map((item: any) => item.id).join(',');
        console.log(reviewers);
        this.formGroup.get('reviewers').setValue(reviewers);
    }
}
