// src/app/components/process-flow.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, SimpleChanges } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { TabActionButtonsComponent } from '../tab-action-buttons/tab-action-buttons.component';
import { FormActionService } from 'src/app/services/ptm/manufacturing-technology-records/form-action.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
import { combineLatest, map } from 'rxjs';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { FullscreenToggleDirective } from 'src/app/shared/directives/fullscreen-toggle.directive';
import { CLDocsService } from 'src/app/services/ptm/manufacturing-technology-records/CL-docs/CL-docs.service';
import { STATUS_MAP } from 'src/app/models/constant/pms';
import { AlertService } from 'src/app/shared/services/alert.service';
import { CLDocDto, CLDocRequest, CLDocsSaveData, CLDocViewModel, UpdateCLDocsPayload } from 'src/app/models/interface/ptm/CL-docs';

@Component({
    selector: 'app-CL-docs',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        UploadCustomComponent,
        TabActionButtonsComponent,
        ComboboxNonRSQLComponent,
        FullscreenToggleDirective,
    ],
    templateUrl: './CL-docs.component.html',
    styleUrls: ['./CL-docs.component.scss'],
    providers: [DatePipe, FormActionService],
})
export class CLDocsComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống

    @Input() mode: 'view' | 'create' | 'edit' = 'create';
    @Input() version: { status: number } = { status: 1 };
    @Input() productVersionId: number;
    @Input() phase: number;
    @Input() productInstructionId: number;
    // 📤 OUTPUTS emit về cha

    @Output() changed = new EventEmitter<string>();
    @Output() submitted = new EventEmitter<CLDocViewModel[]>();
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    formGroup!: FormGroupCustom<{ items: CLDocViewModel[] }>;
    CLDocuments: CLDocDto[] = [];
    statusMap = STATUS_MAP;
    // Kết hợp hai stream thành một Observable<boolean>
    public isBusy$ = combineLatest([this.formSvc.isSaving$, this.fileUploadManagerService.isUploading$]).pipe(
        map(([saving, uploading]) => saving || uploading),
    );

    versionConfigs = [
        {
            url: '/pr/api/quality-control/document',
            fieldValue: 'versionId',
            fieldLabel: 'versionName',
            param: 'versionName',
        },
        {
            url: '/pr/api/quality-control/document',
            fieldValue: 'versionId',
            fieldLabel: 'versionName',
            param: 'versionName',
        },
        {
            url: '/pr/api/quality-control/document',
            fieldValue: 'versionId',
            fieldLabel: 'versionName',
            param: 'versionName',
        },
    ];

    constructor(
        private fb: FormBuilder,
        private CLDocsService: CLDocsService,
        public formSvc: FormActionService<CLDocsSaveData>,
        private authService: AuthService,
        private datePipe: DatePipe,
        private fileUploadManagerService: FileUploadManagerService,
        private alertService: AlertService,
    ) {}
    createCLDocDto(docType: string): CLDocDto {
        return {
            id: 0,
            docType,
            versionName: '',
            formName: '',
            status: 0,
            qualityControlDocumentId: 0,
            updated: 0,
            updatedBy: '',
            action: 1,
        };
    }
    ngOnInit() {
        console.log('🧹 [cl-docs] mounted');

        if ((this.mode === 'edit' || this.mode === 'view') && this.productInstructionId) {
            // Nếu là edit, chỉ build form khi có dữ liệu trả về
            this.CLDocsService.getCLDocuments(this.productInstructionId).subscribe({
                next: (docs) => {
                    if (docs && docs.qualityDocumentDtos.length > 0) {
                        this.CLDocuments = docs.qualityDocumentDtos;
                        // Build form từ dữ liệu thực tế
                        this.formGroup = new FormGroupCustom(this.fb, {
                            items: new FormArrayCustom(
                                docs && docs.qualityDocumentDtos.length > 0 ? docs.qualityDocumentDtos.map((doc) => this.createRow({ ...doc, action: 2 })) : [], // Nếu không có docs thì FormArray rỗng
                            ),
                        });
                    } else {
                        this.CLDocuments = [];
                        this.formGroup = new FormGroupCustom(this.fb, {
                            items: new FormArrayCustom([
                                this.createRow(this.createCLDocDto('Quality Control Plan')),
                                this.createRow(this.createCLDocDto('IQC')),
                                this.createRow(this.createCLDocDto('FQC')),
                            ]),
                        });
                    }
                    if (this.mode === 'view') {
                        this.disableFormControls();
                    }
                },
                error: (err) => {
                    console.log('Lỗi khi lấy tài liệu khác', err.message);
                    // (Có thể build form rỗng hoặc báo lỗi)
                    this.formGroup = new FormGroupCustom(this.fb, { items: new FormArrayCustom([]) });
                },
            });
        } else {
            // Nếu là tạo mới hoặc view thì vẫn tạo form mặc định như cũ
            this.formGroup = new FormGroupCustom(this.fb, {
                items: new FormArrayCustom([
                    this.createRow(this.createCLDocDto('Quality Control Plan')),
                    this.createRow(this.createCLDocDto('IQC')),
                    this.createRow(this.createCLDocDto('FQC')),
                ]),
            });
        }

        this.formSvc.initialize({
            saveApi: (data: CLDocsSaveData) => this.CLDocsService.saveCLDocs(data.productInstructionId, data.payload),
        });
    }
    get items(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('items') as FormArrayCustom<FormGroup>;
    }
    disableFormControls() {
        if (this.formGroup) {
            this.formGroup.disable({ onlySelf: true, emitEvent: false });
        }
    }
    private createRow(data?: CLDocDto) {
        console.log('data', data);
        return this.fb.group({
            id: [data?.id || 0],
            docType: [data?.docType],
            versionName: [data?.versionName ?? ''],
            qualityControlDocumentId: [data?.qualityControlDocumentId || null],
            formName: [data.formName || ''],
            status: [data?.status ?? 0],
            statusText: [this.statusMap[data?.status] || ''],
            lastUpdate: [data?.updated || 0],
            lastUpdateText: [data?.updated ? this.datePipe.transform(data.updated, 'HH:mm dd/MM/yyyy') : ''],
            updatedByEmail: [data?.updatedBy ?? ''],
            // action: 1=create, 2=update, 3=delete
            action: [data?.action ?? 1],
        });
    }
    ngOnChanges(changes: SimpleChanges): void {}

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    onVersionSelect(data: any, rowIndex: number) {
        console.log('data check', data);
        const ctrl = this.items.at(rowIndex);
        if (data && data.versionId) {
            ctrl.patchValue({
                qualityControlDocumentId: data.versionId || null,
                versionName: data.versionName || '',
                status: data.status || null,
                statusText: this.statusMap[data.status],
                lastUpdate: data.lastUpdate || null,
                lastUpdateText: [data?.lastUpdate ? this.datePipe.transform(data.lastUpdate, 'HH:mm dd/MM/yyyy') : ''],
                formName: data.formName,
                updatedByEmail: data.updatedBy,
            });
        } else {
            ctrl.patchValue({
                formName: '',
                versionName: '',
                qualityControlDocumentId: null,
                statusText: '',
                status: 0,
                lastUpdate: 0,
                lastUpdateText: '',
                updatedByEmail: '',
            });
        }
    }
    onSave() {
        if (this.formGroup.invalid) {
            return;
        } else {
            const docs: CLDocViewModel[] = (this.formGroup.get('items') as FormArrayCustom).getRawValue();

            const CLDocuments: CLDocRequest[] = docs
                .filter((d) => d.action !== 0) // hoặc tuỳ logic, giữ lại các row cần gửi
                .map((d) => ({
                    id: d.id,
                    docType: d.docType,
                    qualityControlDocumentId: d.qualityControlDocumentId,

                    action: d.action,
                }));

            const payload: UpdateCLDocsPayload = {
                productVersionId: this.productVersionId,
                phase: this.phase,
                qualityDocuments: CLDocuments,
            };
            console.log('payload', payload);

            const data: CLDocsSaveData = {
                payload: payload,
                productInstructionId: this.productInstructionId || 0,
            };
            this.formSvc.save(data).subscribe({
                next: (resp: CLDocViewModel[]) => {
                    this.alertService.success('Thành công', 'Lưu hồ sơ tài liệu chất lượng thành công');
                    this.exitFullscreenIfNeeded();
                    // Ví dụ side-effect: cập nhật lại UI, emit sự kiện, show toast…
                    this.submitted.emit(resp);
                },
                error: (err) => {
                    console.error('Save failed', err);
                },
            });
            return;
        }
    }
    exitFullscreenIfNeeded() {
        document.body.classList.remove('fullscreen');
    }

    /** Kéo xử lý thành công thì emit và reset */
    // protected onSubmit(value) {
    //     console.log('value form submit', value);
    //     console.log('this.items.value', this.items.value);
    //     this.formGroup.resetForm();
    // }
    ngOnDestroy(): void {
        console.log('🧹 [CL-docs] Unmounted');
    }
}
