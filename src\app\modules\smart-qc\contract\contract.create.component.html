<style>
    .p-button .p-button-icon-only {
        padding: 0.3rem;
    }
</style>

<form #contractForm="ngForm" (ngSubmit)="saveContract(contractForm)">
    <app-sub-header
        [items]="[
            { label: 'Quản lý dự án', url: '/sqc/contract' },
            { label: contract.id ? contract.name : 'Tạo dự án' },
        ]"
        [action]="dcm"
    ></app-sub-header>

    <ng-template #dcm>
        <p-button label="Lưu" type="submit" severity="success" size="small" />
        <p-button label="Đóng" routerLink="/sqc/contract" severity="secondary" size="small" />
    </ng-template>
    <div class="tw-p-5">
        <div class="tw-bg-white tw-rounded-md" style="padding: 1rem">
            <div class="tw-grid tw-gap-4 md:tw-grid-cols-3 sm:tw-grid-cols-1">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <label htmlFor="name" class="tw-font-bold">
                        Tên dự án
                        <span class="tw-text-red-600">(*)</span>
                        :
                    </label>
                    <input
                        [disabled]="!isAdminOrPM"
                        #name="ngModel"
                        [(ngModel)]="contract.name"
                        id="name"
                        name="name"
                        style="width: 100%"
                        (focusout)="trimName(name)"
                        pInputText
                        type="text"
                        required
                    />
                    <div class="text-red-600" *ngIf="contract.name?.length > 255">
                        Tên dự án không vượt quá 255 kí tự
                    </div>
                    <div *ngIf="name.errors && (name.touched || contractForm.submitted)" class="text-red-600">
                        <div *ngIf="name.errors['required']">Tên dự án là trường bắt buộc</div>
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <label htmlFor="code" class="tw-font-bold">Mã dự án :</label>
                    <input
                        [(ngModel)]="contract.code"
                        name="code"
                        id="code"
                        [disabled]="true"
                        placeholder="Hệ thống tự sinh"
                        style="width: 100%"
                        pInputText
                        type="text"
                    />
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <label htmlFor="status" class="tw-font-bold">
                        Trạng thái
                        <span class="tw-text-red-600">(*)</span>
                        :
                    </label>
                    <p-dropdown
                        [disabled]="!isAdminOrPM"
                        #status="ngModel"
                        id="status"
                        name="status"
                        [(ngModel)]="contract.status"
                        [options]="[
                            { value: 0, name: 'Chưa thực hiện' },
                            { value: 1, name: 'Đang thi công' },
                            { value: 2, name: 'Đã hoàn thành' },
                        ]"
                        optionLabel="name"
                        optionValue="value"
                        [style]="{ width: '100%' }"
                        placeholder="Trạng thái dự án"
                        [showClear]="true"
                        required
                    ></p-dropdown>
                    <div *ngIf="status.errors && (status.touched || contractForm.submitted)" class="text-red-600">
                        <div *ngIf="status.errors['required']">Trạng thái là trường bắt buộc</div>
                    </div>
                </div>
            </div>

            <div class="tw-grid tw-gap-4 md:tw-grid-cols-3 sm:tw-grid-cols-1 tw-mt-2">
                <div class="tw-flex tw-flex-col tw-space-y-3">
                    <label htmlFor="timeSpan" class="tw-font-bold">
                        Thời gian dự kiến
                        <span class="tw-text-red-600">(*)</span>
                        :
                    </label>
                    <div id="timeSpan">
                        <p-calendar
                            [disabled]="!isAdminOrPM"
                            [keepInvalid]="true"
                            dateFormat="dd/mm/yy"
                            #startTime="ngModel"
                            [(ngModel)]="contract.startTime"
                            name="startTime"
                            required
                            [style]="{ width: '100%', 'margin-bottom': '15px' }"
                            [showIcon]="true"
                            inputId="icondisplay"
                        />
                        <p-calendar
                            [disabled]="!isAdminOrPM"
                            [keepInvalid]="true"
                            dateFormat="dd/mm/yy"
                            #endTime="ngModel"
                            [(ngModel)]="contract.endTime"
                            name="endTime"
                            required
                            [style]="{ width: '100%' }"
                            [showIcon]="true"
                            inputId="icondisplay"
                        />
                    </div>
                    <div
                        *ngIf="
                            startTimeModel?.value &&
                            endTimeModel?.value &&
                            !startTimeModel?.value.length &&
                            !endTimeModel?.value.length &&
                            startTimeModel?.value?.getTime() > endTimeModel?.value?.getTime()
                        "
                        class="tw-text-red-600"
                    >
                        Ngày bắt đầu không được lớn hơn ngày kết thúc
                    </div>
                    <div
                        *ngIf="
                            (startTime.errors || endTime.errors) &&
                            (startTime.touched || endTime.touched || contractForm.submitted)
                        "
                        class="text-red-600"
                    >
                        <div
                            *ngIf="
                                (startTime.errors && startTime.errors['required']) ||
                                (endTime.errors && endTime.errors['required'])
                            "
                        >
                            Thời gian dự kiến là trường bắt buộc
                        </div>
                    </div>
                    <div *ngIf="startTimeModel?.value?.length || endTimeModel?.value?.length" class="tw-text-red-600">
                        Thời gian phải có dạng dd/mm/yyyy
                    </div>
                </div>
                <div class="tw-flex tw-flex-col tw-space-y-3 md:tw-col-span-2 sm:tw-col-span-1">
                    <label htmlFor="description" class="tw-font-bold">Mô tả :</label>
                    <textarea
                        [disabled]="!isAdminOrPM"
                        [(ngModel)]="contract.description"
                        name="description"
                        id="description"
                        style="width: 100%"
                        rows="4.5"
                        cols="30"
                        pInputTextarea
                        [autoResize]="true"
                    ></textarea>
                </div>
            </div>
        </div>

        <div class="tw-bg-white tw-rounded-md tw-mt-5" style="padding: 1rem">
            <div class="tw-mb-5 tw-flex tw-justify-between">
                <div class="tw-font-bold">Thông tin chung</div>
                <app-button-group-file
                    class=""
                    [urlError]="urlError"
                    service="/smart-qc/api"
                    [disabled]="false"
                    (onFileSelected)="handleSelectFile($event)"
                    (onClearFile)="handleClearFile()"
                    (onClickDowload)="handleDownloadFile()"
                    [types]="typesAccept"
                    errorWrongFileMessage="Vui lòng thử lại với file excel"
                >
                </app-button-group-file>
            </div>
            <div class="tw-grid tw-gap-4 md:tw-grid-cols-2 sm:tw-grid-cols-1">
                <div>
                    <div class="tw-mb-5 tw-font-bold">Phân loại lỗi</div>
                    <div #scrollErrorContainer class="tw-mb-5" style="overflow-x: auto; height: 400px;">
                        <p-table [value]="errorsFormArray.controls">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th></th>
                                    <th>STT</th>
                                    <th>Phân loại lỗi</th>
                                    <th>Mức độ</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                <tr *ngIf="item.value.isEdit" [formGroup]="item">
                                    <td style="width: 40px">
                                        <div class="tw-flex">
                                            <p-button
                                                icon="pi pi-save"
                                                severity="success"
                                                [rounded]="true" [text]="true"
                                                (onClick)="saveError(rowIndex)"
                                                pTooltip="Lưu"
                                                tooltipPosition="top">
                                            </p-button>
                                            <p-button
                                                icon="pi pi-times"
                                                severity="danger"
                                                [rounded]="true" [text]="true"
                                                (onClick)="cancelEditError(rowIndex)"
                                                pTooltip="Huỷ"
                                                tooltipPosition="top">
                                            </p-button>
                                        </div>
                                    </td>
                                    <td>{{ rowIndex + 1 }}</td>
                                    <td>
                                        <input
                                            pInputText
                                            class="tw-w-full"
                                            formControlName="name"
                                            placeholder="Phân loại lỗi"
                                        />
                                        <app-input-validate
                                            [control]="errorsFormArray.controls[rowIndex].get('name')"
                                            fieldName="phân loại lỗi"
                                        />
                                    </td>
                                    <td>
                                        <p-dropdown
                                            [style]="{ width: '100%' }"
                                            id="dropdownField"
                                            [options]="errorLevelOptions"
                                            formControlName="level"
                                            placeholder="Chọn mức độ"
                                            [appendTo]="'body'"
                                        >
                                        </p-dropdown>
                                        <app-input-validate
                                            [control]="errorsFormArray.controls[rowIndex].get('level')"
                                            fieldName="mức độ"
                                        />
                                    </td>
                                </tr>
                                <tr *ngIf="!item.value.isEdit" [formGroup]="item">
                                    <td style="width: 40px">
                                        <div *ngIf="!editingError && !addingError" class="tw-flex">
                                            <p-button
                                                icon="pi pi-pencil"
                                                severity="success"
                                                [rounded]="true" [text]="true"
                                                (onClick)="editError(rowIndex)"
                                                pTooltip="Sửa"
                                                tooltipPosition="top">
                                            </p-button>
                                            <p-button
                                                *ngIf="!item.value.isUsed"
                                                icon="pi pi-trash"
                                                severity="danger"
                                                [rounded]="true" [text]="true"
                                                (onClick)="removeError(rowIndex)"
                                                pTooltip="Xóa"
                                                tooltipPosition="top">
                                            </p-button>
                                        </div>
                                    </td>
                                    <td>{{ rowIndex + 1 }}</td>
                                    <td>{{ item.get('name')?.value }}</td>
                                    <td>{{ item.get('level')?.value }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                    <div>
                        <p-button
                            size="small"
                            label="Thêm phân loại lỗi"
                            [raised]="true"
                            icon="pi pi-plus"
                            [disabled]="addingError"
                            (onClick)="addError()"
                        />
                    </div>
                </div>
                <div>
                    <div class="tw-mb-5 tw-font-bold">Danh sách card</div>
                    <div #scrollCardContainer class="tw-mb-5" style="overflow-x: auto; height: 400px;">
                        <p-table [value]="cardsFormArray.controls">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th></th>
                                    <th>STT</th>
                                    <th>Tên card</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                <tr *ngIf="item.value.isEdit" [formGroup]="item">
                                    <td style="width: 40px">
                                        <div class="tw-flex">
                                            <p-button
                                                icon="pi pi-save"
                                                severity="success"
                                                [rounded]="true" [text]="true"
                                                (onClick)="saveCard(rowIndex)"
                                                pTooltip="Lưu"
                                                tooltipPosition="top">
                                            </p-button>
                                            <p-button
                                                icon="pi pi-times"
                                                severity="danger"
                                                [rounded]="true" [text]="true"
                                                (onClick)="cancelEditCard(rowIndex)"
                                                pTooltip="Huỷ"
                                                tooltipPosition="top">
                                            </p-button>
                                        </div>
                                    </td>
                                    <td>{{ rowIndex + 1 }}</td>
                                    <td>
                                        <input
                                            pInputText
                                            class="tw-w-full"
                                            formControlName="name"
                                            placeholder="Tên card"
                                        />
                                        <app-input-validate
                                            [control]="cardsFormArray.controls[rowIndex].get('name')"
                                            fieldName="tên card"
                                        />
                                    </td>
                                </tr>
                                <tr *ngIf="!item.value.isEdit" [formGroup]="item">
                                    <td style="width: 40px">
                                        <div *ngIf="!editingCard && !addingCard" class="tw-flex">
                                            <p-button
                                                icon="pi pi-pencil"
                                                severity="success"
                                                [rounded]="true" [text]="true"
                                                (onClick)="editCard(rowIndex)"
                                                pTooltip="Sửa"
                                                tooltipPosition="top">
                                            </p-button>
                                            <p-button
                                                *ngIf="!item.value.isUsed"
                                                icon="pi pi-trash"
                                                severity="danger"
                                                [rounded]="true" [text]="true"
                                                (onClick)="removeCard(rowIndex)"
                                                pTooltip="Xóa"
                                                tooltipPosition="top">
                                            </p-button>
                                        </div>
                                    </td>
                                    <td>{{ rowIndex + 1 }}</td>
                                    <td>{{ item.get('name')?.value }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                    <div>
                        <p-button
                            size="small"
                            label="Thêm card"
                            [raised]="true"
                            icon="pi pi-plus"
                            [disabled]="addingCard"
                            (onClick)="addCard()"
                        />
                    </div>
                </div>
            </div>
        </div>

        <section class="tw-grid lg:tw-grid-cols-3 sm:tw-grid-cols-2 tw-gap-4 tw-mt-8">
            <div class="tw-col-span-1 tw-bg-white tw-rounded-xl tw-p-4" style="height: 600px; overflow-y: auto">
                <div class="tw-flex tw-flex-col border-bottom-2 border-gray-300">
                    <div class="tw-font-bold tw-mb-1">Danh sách công việc :</div>

                    <p-button
                        [ngStyle]="{ visibility: isAdminOrPM ? 'visible' : 'hidden' }"
                        class="tw-flex tw-justify-end tw-mb-3"
                        size="small"
                        label="Thêm công việc"
                        [raised]="true"
                        icon="pi pi-plus"
                        severity="success"
                        (onClick)="openEditAction({}, true); ngForm.submitted = false"
                    />
                </div>
                <div
                    cdkDropList
                    [cdkDropListData]="contract.actions"
                    class="drag-custom-list"
                    (cdkDropListDropped)="dropAction($event)"
                >
                    <div
                        cdkDrag
                        (click)="selectAction(action)"
                        [ngClass]="{
                            'drag-custom-box tw-p-3 tw-rounded-xl tw-mt-4 c-pointer': true,
                            'tw-bg-blue-100': selectedAction === action,
                            'border-1 border-gray-200': selectedAction !== action,
                        }"
                        *ngFor="let action of contract.actions; let i = index"
                    >
                        <section class="tw-grid lg:tw-grid-cols-4 tw-grid-cols-2">
                            <div class="lg:tw-col-span-3 tw-rounded-xl tw-text-vertical-center">
                                <span>{{ action.name }}</span>
                            </div>
                            <div class="tw-col-span-1 tw-rounded-xl">
                                <div class="tw-flex relative tw-justify-end">
                                    <p-button
                                        [ngStyle]="{ visibility: isAdminOrPM ? null : 'hidden' }"
                                        class="tw-mr-2"
                                        icon="pi pi-pencil"
                                        severity="info"
                                        [rounded]="true"
                                        [outlined]="true"
                                        size="small"
                                        (click)="openEditAction(action, false); ngForm.submitted = false"
                                    />

                                    <p-button
                                        *ngIf="isAdminOrPM && (action.canDelete || !action.id)"
                                        icon="pi pi-trash"
                                        severity="danger"
                                        [rounded]="true"
                                        [outlined]="true"
                                        size="small"
                                        (click)="deleteAction(action, i)"
                                    />
                                </div>
                            </div>
                        </section>

                        <!--                        <div class="tw-flex  relative">-->
                        <!--                            -->
                        <!--                            -->
                        <!--                        </div>-->
                    </div>
                </div>
            </div>

            <div
                class="lg:tw-col-span-2 sm:tw-col-span-1 tw-bg-white tw-rounded-xl tw-p-4"
                style="height: 600px; overflow-y: auto"
            >
                <app-task-table
                    [canOpenEditForm]="contract.actions.length === 0"
                    [contractId]="this.selectedAction?.contractId"
                    [actionId]="this.selectedAction.id"
                    [templateId]="this.selectedAction?.templateId"
                    [tasks]="this.selectedAction.tasks"
                    [setTask]="setTask"
                    [userList]="userList"
                    [areaList]="areaList"
                    [districtList]="districtList"
                ></app-task-table>
            </div>
        </section>
    </div>
</form>

<p-dialog
    [header]="isNewAction ? 'Thêm công việc' : 'Sửa công việc'"
    [(visible)]="visibleActionForm"
    [modal]="true"
    [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
    [style]="{ width: '40vw' }"
    [draggable]="true"
    [resizable]="true"
>
    <form [formGroup]="actionForm" (ngSubmit)="saveAction()" #ngForm="ngForm">
        <div class="tw-mb-5">
            <div class="tw-mb-1">
                Tên công việc
                <span class="tw-text-red-600">*</span>
                :
            </div>
            <input
                formControlName="name"
                style="width: 100%"
                pInputText
                id="header"
                class="flex-auto tw-border-2 tw-border-gray-300"
            />
            <div
                *ngIf="actionForm.get('name').errors && (actionForm.get('name').touched || ngForm.submitted)"
                class="text-red-600 tw-pt-1"
            >
                <div *ngIf="actionForm.get('name').errors['required']">Tên công việc là trường bắt buộc</div>
                <div *ngIf="actionForm.get('name').errors['maxlength']">
                    Tên công việc không được vượt quá 255 ký tự
                </div>
            </div>
        </div>
        <div class="tw-mb-5">
            <div class="tw-mb-1">
                Mẫu kiểm tra
                <span class="tw-text-red-600">*</span>
                :
            </div>
            <app-form-input
                [control]="actionForm"
                controlName="templateId"
                [callback]="setActionTemplate"
                [options]="selectedAction.template ? [selectedAction.template] : []"
                url="/smart-qc/api/template/search?query="
                itemLable="name"
                itemValue="id"
                placeHolder="Chọn mẫu kiểm tra"
                [isMultiple]="false"
            ></app-form-input>
            <div
                *ngIf="
                    actionForm.get('templateId').errors && (actionForm.get('templateId').touched || ngForm.submitted)
                "
                class="text-red-600 tw-pt-1"
            >
                <div *ngIf="actionForm.get('templateId').errors['required']">Mẫu kiểm tra là trường bắt buộc</div>
            </div>
        </div>
        <div class="tw-mb-5">
            <div class="tw-mb-1">Mô tả :</div>
            <input
                formControlName="description"
                style="width: 100%"
                pInputText
                id="content"
                class="flex-auto tw-border-2 tw-border-gray-300"
            />
        </div>
        <div class="flex justify-content-end gap-2">
            <p-button label="Lưu" severity="success" type="submit" />
            <p-button label="Đóng" severity="secondary" (click)="visibleActionForm = false" />
        </div>
    </form>
</p-dialog>
