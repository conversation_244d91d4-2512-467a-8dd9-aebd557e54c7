:host ::ng-deep {
    /* tag custom */
    .p-tag-cutom .p-tag {
        background-color: var(--surface-100);
        color: #1c1a1a;
        border: 0.005px solid #bbabab;
        margin-left: 2px;
    }

    /* dropdown / multiselect / password đều full-width */
    .p-multiselect,
    .p-dropdown,
    .p-password {
        width: 100%;
    }

    /* chung cho input-switch trong 3 trạng thái */
    @each $state, $color in (success: #00b87b, warning: #fba65d, danger: #f4516c) {
        .#{$state} > .p-inputswitch {
            .p-inputswitch-slider {
                border: 1px solid $color;
                background: #d4d0d0;
            }
            &.p-inputswitch-checked .p-inputswitch-slider {
                background: $color;
            }
        }
    }
}

/*------------ Khung filter (grid) ------------*/
.filter-container {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
    border-radius: 4px;
}

/*------------ Nhóm action buttons cuối form ------------*/
.action-buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 0.75rem;
    margin: 1rem 0;
}
:host ::ng-deep .p-inputtext[disabled] {
    opacity: 1;
    color: #333;
}
