import { Injectable } from '@angular/core';
import { Subject, Observable, EMPTY } from 'rxjs';
import { exhaustMap, catchError, tap } from 'rxjs/operators';

interface CallOptions<T> {
    apiCall: () => Observable<T>;
    onSuccess?: (res: T) => void;
    onError?: (err: any) => void;
}

@Injectable({ providedIn: 'root' })
export class SafeApiCallerService {
    private trigger$ = new Subject<CallOptions<any>>();

    constructor() {
        this.trigger$
            .pipe(
                exhaustMap(({ apiCall, onSuccess, onError }) =>
                    apiCall().pipe(
                        tap((res) => onSuccess?.(res)),
                        catchError((err) => {
                            onError?.(err);
                            return EMPTY;
                        })
                    )
                )
            )
            .subscribe();
    }

    call<T>(options: CallOptions<T>) {
        this.trigger$.next(options);
    }
}
