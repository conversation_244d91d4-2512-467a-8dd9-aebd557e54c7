// src/app/shared/components/routing/routing.component.ts
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, Input, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { ButtonModule } from 'primeng/button';
import { PatentGraphComponent } from './components/chart/patent-graph.component';
import { Observable } from 'rxjs';
import { Routing } from 'src/app/models/interface/ptm/routing';
import { RoutingService } from 'src/app/services/ptm/routing/routing.service';
import { ProcessFlowService } from 'src/app/services/ptm/process-flow/process-flow.service';
import { MessageService } from 'primeng/api';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import { FullscreenToggleDirective } from 'src/app/shared/directives/fullscreen-toggle.directive';
import { TrackChangesComponent } from '../track-changes/track-changes.component';
@Component({
    selector: 'app-routing',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextareaModule,
        InfoShareComponent,
        ButtonModule,
        PatentGraphComponent,
        FullscreenToggleDirective,
        TrackChangesComponent,
    ],
    templateUrl: './routing.component.html',
    styleUrls: ['./routing.component.scss'],
    providers: [RoutingService],
})
export class RoutingComponent implements OnInit, OnDestroy {
    nodes = [{ id: 'Apple' }, { id: 'Samsung' }, { id: 'Google' }, { id: 'Motorola' }];

    links = [
        { source: 'Apple', target: 'Samsung' },
        { source: 'Apple', target: 'Motorola' },
        { source: 'Google', target: 'Apple' },
    ];
    @Input() title: string = '';
    @Input() isSaving!: Observable<boolean>;
    @Input() isSubmitting!: Observable<boolean>;
    @Input() isApproving!: Observable<boolean>;
    @Input() currentProduct: any;
    @Input() productVersionId: number;
    @Input() phase: number;
    @Input() productionInstructionId: number;
    name: string;
    detailRouting: any;
    dataOC: any;
    oldRouting: Routing = {
        routingId: 0,
        chartUrl: '',
        versionId: 0,
        phase: 0,
        reviewerIds: [],
        routingDetails: [],
        chartType: 2, // 0 : auto, 1: upload file
    };
    instructionId: string;
    formGroup: FormGroupCustom<Routing>;
    downloadBaseUrl: string = environment.STORAGE_BASE_URL;
    urlFile: string;
    showTrackDialog: boolean;
    selectedTab: number = 2;

    constructor(
        private routingService: RoutingService,
        private messageService: MessageService,
        private processFlowService: ProcessFlowService,
        private fb: FormBuilder,
        private http: HttpClient,
    ) {}

    content = '';
    nameFile: string;
    getData(): string {
        return this.content;
    }
    ngOnInit(): void {
        // console.log('✅ [routing] Mounted');
        this.instructionId = this.productionInstructionId.toString();
        this.initForm();
        this.getRouting();
        this.name = 'RT-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['productionInstructionId'] && changes['productionInstructionId'].currentValue) {
            this.instructionId = this.productionInstructionId.toString();
            this.getRouting();
        }
    }

    ngOnDestroy(): void {
        console.log('🧹 [routing] Unmounted');
    }

    getRouting() {
        this.routingService.getRouting(this.instructionId).subscribe({
            next: (res) => {
                this.detailRouting = res;
                this.formGroup.patchValue({
                    reviewerIds: this.detailRouting?.instructionInfo?.reviewers.map((item: any) => item.id),
                    chartUrl: this.detailRouting?.chartUrl,
                    routingId: this.detailRouting?.id,
                    chartType: this.detailRouting?.chartType,
                });
                if (this.formGroup.get('chartType')?.value === 1) {
                    const url = this.downloadBaseUrl + '/' + this.formGroup.get('chartUrl')?.value;
                    this.urlFile = url;
                    this.http
                        .get(url, {
                            responseType: 'blob',
                            observe: 'response',
                        })
                        .subscribe((response) => {
                            // ✅ Lấy tất cả header
                            const headers = response.headers;

                            // ✅ Lấy từng header cụ thể
                            const contentDisposition = headers.get('Content-Disposition');
                            // (Optional) Extract filename từ header nếu có
                            let fileName = 'downloaded-file.json';
                            const match = contentDisposition.match(/filename\*=UTF-8''(.+)$/);

                            if (match && match[1]) {
                                try {
                                    fileName = decodeURIComponent(match[1]);
                                } catch (e) {
                                    console.error('Lỗi giải mã tên file:', e);
                                }
                            }

                            this.nameFile = fileName;
                        });
                } else if (this.formGroup.get('chartType')?.value === 0) {
                    const url = this.downloadBaseUrl + '/' + this.formGroup.get('chartUrl')?.value;
                    this.urlFile = url;
                    this.http.get(url).subscribe((response: any) => {
                        this.nodes = response.nodes;
                        this.links = response.links;
                    });
                }
            },
            error: () => {},
        });
    }

    initForm() {
        this.formGroup = new FormGroupCustom(this.fb, {
            routingId: [this.oldRouting?.routingId],
            chartUrl: [this.oldRouting?.chartUrl],
            versionId: [this.oldRouting?.versionId],
            phase: [this.oldRouting?.phase],
            reviewerIds: [this.oldRouting?.reviewerIds],
            routingDetails: [this.oldRouting?.routingDetails],
            chartType: [this.oldRouting?.chartType],
        });
    }

    handleApproverChange(event: any) {
        this.formGroup.patchValue({
            reviewerIds: event.map((item: any) => item.id),
        });
    }

    onSelectFile(event: any, type: number) {
        if (this.instructionId === '0') {
            this.messageService.add({
                key: 'app-alert',
                severity: 'error',
                summary: 'Lỗi',
                detail: 'Không thể thực hiện do Process Flow chưa có dữ liệu',
            });
            return;
        }
        const file = event.target.files[0];
        this.routingService.uploadFile(file).subscribe({
            next: (res: any) => {
                if (type === 1) {
                    this.nameFile = file.name;
                }

                this.urlFile = this.downloadBaseUrl + res.path;

                this.formGroup.patchValue({
                    chartUrl: res.path,
                    chartType: type,
                });
            },
            error: () => {},
        });
    }

    upFile(file: File, type: number) {
        this.routingService.uploadFile(file).subscribe({
            next: (res: any) => {
                this.formGroup.patchValue({
                    chartUrl: res.path,
                    chartType: type,
                });
            },
            error: () => {},
        });
    }

    clearFile() {
        this.nameFile = '';
        this.formGroup.patchValue({
            chartUrl: '',
        });
    }

    downloadFile() {
        const url = this.downloadBaseUrl + this.formGroup.get('chartUrl')?.value;
        const fileName = this.nameFile || 'download.json';

        this.http.get(url, { responseType: 'blob' }).subscribe((blob) => {
            const a = document.createElement('a');
            const objectUrl = URL.createObjectURL(blob);
            a.href = objectUrl;
            a.download = fileName;
            a.click();
            URL.revokeObjectURL(objectUrl);
        });
    }

    createAutoRouting() {
        if (this.instructionId === '0') {
            this.messageService.add({
                key: 'app-alert',
                severity: 'error',
                summary: 'Lỗi',
                detail: 'Không thể thực hiện do Process Flow chưa có dữ liệu',
            });
            return;
        }
        this.processFlowService.getProcessFlow(this.instructionId).subscribe({
            next: (res: any) => {
                const nodesSet = new Set<string>();
                this.links = [];
                res.processFlows.forEach((item) => {
                    const oc = item.oc?.trim();
                    const nextOc = item.nextOc?.trim();

                    if (oc) nodesSet.add(oc);
                    if (nextOc) nodesSet.add(nextOc);

                    // ✅ Chỉ thêm link nếu có nextOc
                    if (oc && nextOc) {
                        this.links.push({ source: oc, target: nextOc });
                    }
                });

                this.nodes = Array.from(nodesSet).map((id) => ({ id }));

                this.formGroup.patchValue({
                    chartType: 0,
                });
                this.formGroup.get('chartType')?.updateValueAndValidity();
                this.nameFile = '';
                const graphData = {
                    nodes: this.nodes.map((d: any) => ({
                        id: d.id,
                        x: d.x,
                        y: d.y,
                        fx: d.fx ?? null,
                        fy: d.fy ?? null,
                    })),
                    links: this.links.map((d: any) => ({
                        source: typeof d.source === 'object' ? d?.source?.id : d?.source,
                        target: typeof d.target === 'object' ? d?.target?.id : d?.target,
                    })),
                };

                const graphJSON = JSON.stringify(graphData);

                const blob = new Blob([graphJSON], { type: 'application/json' });
                const file = new File([blob], 'graph-data.json', { type: 'application/json' });

                this.upFile(file, 0);
            },
            error: () => {},
        });
    }

    openTrackDialog(tab: number): void {
        this.showTrackDialog = true;
        this.selectedTab = tab;
    }

    handleCloseTrackDialog(): void {
        this.showTrackDialog = false;
    }

    handlePreview() {
        const payload = {
            tabType: 2,
        };
        this.processFlowService.previewProcessFlow(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Gửi preview thành công',
                });
                this.getRouting();
            },
            error: () => {},
        });
    }

    handleComplete() {
        const payload = {
            tabType: 2,
            approvalStatus: 8,
        };
        this.processFlowService.comfirmProcessFlow(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Phê duyệt thành công',
                });
                this.getRouting();
            },
            error: () => {},
        });
    }

    handleReject() {
        const payload = {
            tabType: 2,
            approvalStatus: 4,
        };
        this.processFlowService.comfirmProcessFlow(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Từ chối thành công',
                });
                this.getRouting();
            },
            error: () => {},
        });
    }

    async handleSubmit(value: Routing): Promise<void> {
        if (this.instructionId === '0') {
            this.messageService.add({
                key: 'app-alert',
                severity: 'error',
                summary: 'Lỗi',
                detail: 'Không thể thực hiện do Process Flow chưa có dữ liệu',
            });
            return;
        }
        const payload = {
            reviewerIds: this.formGroup.get('reviewerIds')?.value,
            routingId: this.formGroup.get('routingId')?.value,
            chartUrl: this.formGroup.get('chartUrl')?.value,
            chartType: this.formGroup.get('chartType')?.value,
        };

        this.routingService.create(payload, this.instructionId).subscribe({
            next: (res) => {
                if (this.instructionId) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật routing thành công',
                    });
                } else {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo routing thành công',
                    });
                }
                if (this.instructionId === '0') {
                    this.instructionId = res.id;
                }

                this.getRouting();
            },
            error: () => {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Lỗi',
                    detail: 'Không thể tạo routing',
                });
            },
        });
    }
}
