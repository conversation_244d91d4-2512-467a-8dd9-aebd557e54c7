<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share [title]="title"></app-info-share>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[10px]">
        <label class="tw-font-semibold tw-text-base label">Chi tiết MANBOM</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Xuất ERP" class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"></button>
            <button pButton type="button" label="Track Changes" class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"></button>
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
            <button pButton type="button" label="Import TLTH" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>
    </div>
    <app-form-item label="">
        <app-form #form [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit()">
            <div formArrayName="listManBom">
                <p-panel [toggleable]="true">
                    <p-table styleClass="p-datatable-gridlines" [value]="manBom.controls">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 5rem">STT</th>
                                <th style="min-width: 9rem">Description <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Đơn vị <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Công đoạn <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 5rem">Số lượng/Công đoạn <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Reference</th>
                                <th style="min-width: 9rem">Loại vật tư <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 5rem">Tỷ lệ tiêu hao</th>
                                <th style="min-width: 9rem">Ghi chú</th>
                                <th style="max-width: 5rem">Thao tác</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr [formGroupName]="rowIndex">
                                <!-- STT -->
                                <td>
                                    <app-form-item label=""> {{ rowIndex + 1 }}</app-form-item>
                                </td>
                                <!-- Description -->
                                <td>
                                    <app-form-item label="">
                                        <input class="tw-w-full" pInputText type="text" formControlName="description" />
                                    </app-form-item>
                                </td>
                                <!-- Unit -->
                                <td>
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="unit" />
                                    </app-form-item>
                                </td>
                                <!-- Process -->
                                <td>
                                    <app-form-item label="">
                                        <p-dropdown
                                            [options]="processType"
                                            formControlName="process"
                                            optionLabel="label"
                                            optionValue="value"
                                            class="tw-w-full"
                                            [showClear]="true"
                                            appendTo="body"
                                            [filter]="true"
                                        />
                                    </app-form-item>
                                </td>
                                <!-- Quantity -->
                                <td>
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="quantity" />
                                    </app-form-item>
                                </td>
                                <!-- Reference -->
                                <td>
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="reference" />
                                    </app-form-item>
                                </td>
                                <!-- Material Type -->
                                <td>
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="materialType" />
                                    </app-form-item>
                                </td>
                                <!-- Attrition Rate -->
                                <td>
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="attritionRate" />
                                    </app-form-item>
                                </td>
                                <!-- Note -->
                                <td>
                                    <app-form-item label="">
                                        <input pInputText class="tw-w-full" formControlName="note" />
                                    </app-form-item>
                                </td>
                                <!-- Thao tác -->
                                <td>
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            pTooltip="Hủy"
                                            tooltipPosition="top"
                                            type="button"
                                            (click)="removeItem(rowIndex)"
                                        >
                                            <span class="pi pi-times"></span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="tw-mt-3">
                        <p-button label="Thêm dòng" icon="pi pi-plus" severity="info" size="small" (click)="addItem()"></p-button>
                    </div>
                </p-panel>
            </div>

            <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
                <p-button
                    label="Lưu"
                    type="submit"
                    [disabled]="formGroup.invalid || (isSaving | async)"
                    [loading]="isSaving | async"
                    (onClick)="handleSubmit()"
                    loadingIcon="pi pi-spinner pi-spin"
                >
                </p-button>
            </div>
        </app-form>
    </app-form-item>
</ng-container>
