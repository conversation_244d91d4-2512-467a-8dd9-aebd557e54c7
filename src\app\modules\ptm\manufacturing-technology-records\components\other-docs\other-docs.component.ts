// src/app/components/process-flow.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, SimpleChanges, QueryList, ViewChildren } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { AlertService } from 'src/app/shared/services/alert.service';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { OtherDocViewModel, OtherDocRequest, OtherDocsSaveData, UpdateOtherDocsPayload, OtherDocDto } from 'src/app/models/interface/ptm/other-docs';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { OtherDocsService } from 'src/app/services/ptm/manufacturing-technology-records/other-docs/other-docs.service';
import { TabActionButtonsComponent } from '../tab-action-buttons/tab-action-buttons.component';
import { v4 as uuid } from 'uuid';
import { FormActionService } from 'src/app/services/ptm/manufacturing-technology-records/form-action.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
import { combineLatest, finalize, map, Subscription, switchMap, tap } from 'rxjs';
import { UploadService } from 'src/app/services/upload/upload.service';
import { FullscreenToggleDirective } from 'src/app/shared/directives/fullscreen-toggle.directive';

@Component({
    selector: 'app-other-docs',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        UploadCustomComponent,
        TabActionButtonsComponent,
        FullscreenToggleDirective,
    ],
    templateUrl: './other-docs.component.html',
    styleUrls: ['./other-docs.component.scss'],
    providers: [FormActionService, DatePipe],
})
export class OtherDocsComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống

    @Input() mode: 'view' | 'create' | 'edit' = 'create';
    @Input() version: { status: number } = { status: 1 };
    @Input() productVersionId: number;
    @Input() phase: number;
    @Input() productInstructionId: number;
    // 📤 OUTPUTS emit về cha

    @Output() changed = new EventEmitter<string>();
    @Output() submitted = new EventEmitter<OtherDocViewModel[]>();
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    @ViewChildren('OtherDocsUploader') otherDocsUploader!: QueryList<UploadCustomComponent>;
    formGroup!: FormGroupCustom<{ items: OtherDocViewModel[] }>;
    private uploadIds = new Map<number, string>();
    private subs = new Map<number, Subscription>();
    public loadingRows = new Map<number, boolean>();
    public isUploading = false;
    private uploadSub!: Subscription;

    otherDocuments: OtherDocDto[] = [];
    // Kết hợp hai stream thành một Observable<boolean>
    public isBusy$ = combineLatest([this.formSvc.isSaving$, this.fileUploadManagerService.isUploading$]).pipe(
        map(([saving, uploading]) => saving || uploading),
    );

    constructor(
        private fb: FormBuilder,
        private datePipe: DatePipe,
        private otherDocsService: OtherDocsService,
        public formSvc: FormActionService<any>,
        private authService: AuthService,
        private alertService: AlertService,
        private fileUploadManagerService: FileUploadManagerService,
        private uploadService: UploadService,
    ) {}
    ngOnInit() {
        console.log('🧹 [other-docs] mounted');
        this.uploadSub = this.fileUploadManagerService.isUploading$.subscribe((flag) => (this.isUploading = flag));

        if ((this.mode === 'edit' || this.mode === 'view') && this.productInstructionId) {
            // Nếu là edit, chỉ build form khi có dữ liệu trả về
            this.otherDocsService.getOtherDocuments(this.productInstructionId).subscribe({
                next: (docs) => {
                    if (docs && docs.otherDocumentDtos.length > 0) {
                        this.otherDocuments = docs.otherDocumentDtos;
                        // Build form từ dữ liệu thực tế
                        this.formGroup = new FormGroupCustom(this.fb, {
                            items: new FormArrayCustom(
                                docs && docs.otherDocumentDtos.length > 0
                                    ? docs.otherDocumentDtos.map((doc) => this.createRow({ ...doc, action: 2 }, false))
                                    : [], // Nếu không có docs thì FormArray rỗng
                            ),
                        });
                    } else {
                        this.otherDocuments = [];
                        this.formGroup = new FormGroupCustom(this.fb, {
                            items: new FormArrayCustom([
                                this.createRow(this.createOtherDocDto('Layout Line SX'), true),
                                this.createRow(this.createOtherDocDto('HDSD setup'), true),
                                this.createRow(this.createOtherDocDto('HDSD sửa chữa'), true),
                            ]),
                        });
                    }
                    if (this.mode === 'view') {
                        this.disableFormControls();
                    }
                },
                error: (err) => {
                    console.log('Lỗi khi lấy tài liệu khác', err.message);
                    // (Có thể build form rỗng hoặc báo lỗi)
                    this.formGroup = new FormGroupCustom(this.fb, { items: new FormArrayCustom([]) });
                },
            });
        } else {
            // Nếu là tạo mới hoặc view thì vẫn tạo form mặc định như cũ
            this.formGroup = new FormGroupCustom(this.fb, {
                items: new FormArrayCustom([
                    this.createRow(this.createOtherDocDto('Layout Line SX'), true),
                    this.createRow(this.createOtherDocDto('HDSD setup'), true),
                    this.createRow(this.createOtherDocDto('HDSD sửa chữa'), true),
                ]),
            });
        }
        // Disable controls nếu là view mode

        // Khởi tạo service cho cả hai mode, KHÔNG phụ thuộc formGroup
        this.formSvc.initialize({
            saveApi: (data: OtherDocsSaveData) => this.otherDocsService.saveOtherDocs(data.productInstructionId, data.payload),
        });
    }
    createOtherDocDto(name: string): OtherDocDto {
        return {
            id: 0,
            name,
            filePath: '',
            lastUpdate: null,
            userUpdate: '',
            action: 1,
        };
    }
    disableFormControls() {
        if (this.formGroup) {
            this.formGroup.disable({ onlySelf: true, emitEvent: false });
        }
    }
    get items(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('items') as FormArrayCustom<FormGroup>;
    }

    private createRow(data?: OtherDocDto, disable = false) {
        return this.fb.group({
            id: [data?.id ?? Date.now() + Math.floor(Math.random() * 100000)],
            name: [{ value: data?.name || '', disabled: disable }, Validators.required],
            file: null,
            lastUpdate: [data?.lastUpdate || null],
            filePath: [data?.filePath ?? ''],
            userUpdate: [data?.userUpdate ?? ''],
            lastUpdateText: [data?.lastUpdate ? this.datePipe.transform(data.lastUpdate, 'HH:mm dd/MM/yyyy') : ''],
            // action: 1=create, 2=update, 3=delete
            action: [data?.action ?? 1],
        });
    }
    ngOnChanges(changes: SimpleChanges): void {}

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    addItem() {
        this.items.push(this.createRow());
    }

    removeItem(rowId: number) {
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')!.value === rowId);
        if (idx === -1) return;

        const ctrl = this.items.at(idx);

        // Nếu dòng mới (action=1) thì xoá hẳn,
        // ngược lại chỉ đánh dấu xóa để vẫn gửi action=3
        const currentAction = ctrl.get('action')!.value;
        if (currentAction === 1) {
            // mới thêm, chưa sync với BE → xoá hẳn
            this.items.removeAt(idx);
        } else {
            // đã tồn tại trên BE → mark delete
            ctrl.get('action')!.setValue(3);
        }

        // map loadingRows, uploadIds xoá sạch
        this.uploadIds.delete(rowId);
        this.loadingRows.delete(rowId);
    }
    onClearFile(rowId: number) {
        const uploadId = this.uploadIds.get(rowId);
        if (uploadId) {
            this.fileUploadManagerService.cancel(uploadId);
            this.uploadIds.delete(rowId);
        }

        // Reset controls for this row
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx !== -1) {
            const ctrl = this.items.at(idx);
            ctrl.patchValue({ file: null, lastUpdateText: '', lastUpdate: null, userUpdate: '', filePath: '' });
        }
    }
    onFileSelected(files: any[], rowId: number) {
        const file = files[0];
        const fileName: string = files[0].name;
        if (!file) return;
        this.loadingRows.set(rowId, true);
        const uploadId = uuid();
        this.uploadIds.set(rowId, uploadId);

        const currentUser = this.authService.getPrinciple();
        // Find the row form group by id
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx === -1) return;
        const ctrl = this.items.at(idx);

        ctrl.patchValue({
            file,
            lastUpdate: Date.now(),
            lastUpdateText: this.datePipe.transform(Date.now(), 'HH:mm dd/MM/yyyy'),
            userUpdate: currentUser?.email || '',
        });
        const sub = this.uploadService
            .analyzeFile(fileName, 'OTHERS')

            .pipe(
                tap((meta) => {
                    if (ctrl.get('filePath')) ctrl.get('filePath')?.setValue(meta.objectPath);
                }),
                switchMap((meta) => this.uploadService.uploadToPresignedUrl(files[0], meta.presignedUrl)),
                // dù complete hay error, luôn finish uploadId
                finalize(() => this.fileUploadManagerService.finish(uploadId)),
            )
            .subscribe({
                next: (p) => ({}),
                error: (e) => {
                    const list = this.otherDocsUploader.toArray();
                    if (list[idx]) list[idx].clearAll();
                },
                complete: () => {
                    this.loadingRows.set(rowId, false);
                },
            });
        this.subs.set(rowId, sub);
        // Hook cancel callback
        this.fileUploadManagerService.start(uploadId, () => {
            const s = this.subs.get(rowId);
            if (s) {
                s.unsubscribe();
                this.subs.delete(rowId);
            }
        });
    }

    onSave() {
        if (this.formGroup.invalid) {
            return;
        } else {
            const docs: OtherDocViewModel[] = (this.formGroup.get('items') as FormArray).getRawValue();

            const otherDocuments: OtherDocRequest[] = docs
                .filter((d) => d.action !== 0) // hoặc tuỳ logic, giữ lại các row cần gửi
                .map((d) => ({
                    id: d.id,
                    name: d.name,
                    filePath: d.filePath,
                    action: d.action,
                    lastUpdate: typeof d.lastUpdate === 'number' ? d.lastUpdate : d.lastUpdate ? Date.parse(d.lastUpdate) : null,
                    userUpdate: d.userUpdate,
                }));

            const payload: UpdateOtherDocsPayload = {
                productVersionId: this.productVersionId,
                phase: this.phase,
                otherDocuments,
            };
            console.log('payload', payload);

            const data: OtherDocsSaveData = {
                payload: payload,
                productInstructionId: this.productInstructionId || 0,
            };
            this.formSvc.save(data).subscribe({
                next: (resp: OtherDocViewModel[]) => {
                    this.alertService.success('Thành công', 'Lưu hồ sơ tài liệu khác thành công');
                    this.exitFullscreenIfNeeded();
                    // Ví dụ side-effect: cập nhật lại UI, emit sự kiện, show toast…
                    this.submitted.emit(resp);
                },
                error: (err) => {
                    console.error('Save failed', err);
                },
            });
            return;
        }
    }
    // private fromDtoToViewModel(doc: OtherDocDto): OtherDocViewModel {
    //     return {
    //         id: doc.id,
    //         name: doc.name,
    //         file: null,
    //         lastUpdate: doc.updated ? new Date(doc.updated).toLocaleString() : null,
    //         filePath: doc.filePath || '',
    //         updatedBy: doc.updatedBy.email || '',
    //         action: 2, // 2 = update
    //     };
    // }
    exitFullscreenIfNeeded() {
        document.body.classList.remove('fullscreen');
    }
    /** Kéo xử lý thành công thì emit và reset */
    // protected onSubmit(value) {
    //     console.log('value form submit', value);
    //     console.log('this.items.value', this.items.value);
    //     this.formGroup.resetForm();
    // }
    ngOnDestroy(): void {
        this.uploadSub.unsubscribe();
        console.log('🧹 [other-docs] Unmounted');
    }
}
