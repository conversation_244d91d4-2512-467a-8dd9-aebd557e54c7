// src/app/components/process-flow.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractControl, FormArray, FormBuilder, FormGroup, FormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { ProcessFlow, ProcessFlowDetail } from 'src/app/models/interface/ptm/process-flow';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { SYMBOL_TYPE, PRODUCT_PROCESS_TYPE } from 'src/app/models/constant/ptm';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { Observable } from 'rxjs';
import { MessageService } from 'primeng/api';
import { ProcessFlowService } from 'src/app/services/ptm/process-flow/process-flow.service';
import { ActivatedRoute, Router } from '@angular/router';
import * as XLSX from 'xlsx';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import { FullscreenToggleDirective } from 'src/app/shared/directives/fullscreen-toggle.directive';
import { TrackChangesComponent } from '../track-changes/track-changes.component';

@Component({
    selector: 'app-process-flow',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        InfoShareComponent,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        DropdownModule,
        CheckboxModule,
        FullscreenToggleDirective,
        TrackChangesComponent,
    ],
    templateUrl: './process-flow.component.html',
    styleUrls: ['./process-flow.component.scss'],
    providers: [ProcessFlowService],
})
export class ProcessFlowComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() currentProduct: any;
    @Input() productVersionId: number;
    @Input() phase: number;
    @Input() productionInstructionId: number;

    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();
    @Output() changed = new EventEmitter<string>();
    @Output() idProductInstruction = new EventEmitter<number>();

    formGroup: FormGroupCustom<ProcessFlow>;
    symbolType = SYMBOL_TYPE;
    productProcessType = PRODUCT_PROCESS_TYPE;
    oldProcessFlow: ProcessFlow = {
        versionId: 0,
        phase: 0,
        reviewerIds: [],
        listProcessFlow: [],
        processFlows: {},
    };
    disableAutoErrorMessage: boolean = true;
    detailProcessFlow: any;
    name: string;
    instructionId: string;
    downloadBaseUrl: string = environment.STORAGE_BASE_URL;
    dropdownTouched: { [key: number]: boolean } = {};
    requiredColumns = [
        'STEP',
        'Operation Code (OC) *',
        'Next Operation Code (Next OC) *',
        'Đặc tính công việc (Symbol) *\n(điền các giá trị từ 1 - 6 theo Bảng mã)',
        'OPERATION DESCRIPTION (OD)',
        'Product and process characteristics *\n(điền 1 nếu là Man, 2 nếu là Machine, 3 nếu là Man/ Machine)',
        'Online/ Offline *\n(điền 1 nếu là Online, 2 nếu là Offline)',
    ];
    showTrackDialog: boolean;
    selectedTab: number = 1;
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    @Input() isSaving: Observable<boolean>;
    @Input() isSubmitting: Observable<boolean>;
    @Input() isApproving: Observable<boolean>;

    constructor(
        private fb: FormBuilder,
        private processFlowService: ProcessFlowService,
        private messageService: MessageService,
        private route: ActivatedRoute,
        private http: HttpClient,
    ) {
        // console.log('🚀 [ProcessFlowComponent] Constructed', this.oldProcessFlow);
        this.initForm(this.oldProcessFlow);
    }

    ngOnInit(): void {
        this.instructionId = this.productionInstructionId.toString();
        this.initForm(this.oldProcessFlow);
        this.sortProcessFlow();
        this.name = 'PFD-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['oldProcessFlow'] && changes['oldProcessFlow'].currentValue && !changes['oldProcessFlow'].previousValue) {
            this.initForm(this.oldProcessFlow);
            this.sortProcessFlow();
        }
    }

    initForm(data: ProcessFlow | null) {
        this.formGroup = new FormGroupCustom<ProcessFlow>(this.fb, {
            listProcessFlow: new FormArrayCustom(this.initFormContact(data?.listProcessFlow || []), [this.duplicateOcValidator(), this.nextOcValidator()]),
            versionId: [data?.versionId],
            phase: [data?.phase],
            reviewerIds: [data?.reviewerIds],
            processFlows: [data?.processFlows || {}],
        });
    }

    initFormContact(items: ProcessFlowDetail[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    processFlowId: [item?.processFlowId],
                    step: [item?.step],
                    oc: [item?.oc, [Validators.required, Validators.maxLength(4), Validators.pattern('^[A-Z0-9]{1,4}$')]],
                    nextOc: [item?.nextOc],
                    symbol: [item?.symbol, Validators.required],
                    operationDescription: [item?.operationDescription, [Validators.required, Validators.maxLength(100)]],
                    productAndProcessCharacteristics: [item?.productAndProcessCharacteristics, Validators.required],
                    online: [item?.online],
                    offline: [item?.offline],
                    action: [item?.action],
                }),
        );
    }

    getOcErrorMessage(control: AbstractControl | null): string | null {
        if (!control || !control.errors) return null;
        if (control.errors['required']) return 'Trường này là bắt buộc';
        if (control.errors['maxlength']) return 'Không quá 4 ký tự';
        if (control.errors['pattern']) return 'Giá trị không hợp lệ';
        if (control.errors['duplicate']) return 'OC đã tồn tại';
        if (typeof control.errors['customError'] === 'string') return control.errors['customError'];
        return null;
    }

    private duplicateOcValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!(control instanceof FormArray)) return null;

            const formArray = control as FormArray;

            // Reset lỗi duplicate trên tất cả các control
            formArray.controls.forEach((ctrl) => {
                const ocControl = ctrl.get('oc');
                if (!ocControl) return;

                const errors = { ...ocControl.errors };
                if (errors['duplicate']) {
                    delete errors['duplicate'];
                    ocControl.setErrors(Object.keys(errors).length ? errors : null);
                }
            });

            // Thu thập tất cả giá trị OC
            const ocMap: Record<string, number[]> = {};
            formArray.controls.forEach((ctrl, index) => {
                const oc = ctrl.get('oc')?.value;
                if (oc) {
                    if (!ocMap[oc]) ocMap[oc] = [];
                    ocMap[oc].push(index);
                }
            });

            // Gắn lại lỗi duplicate
            Object.entries(ocMap).forEach(([_oc, indices]) => {
                if (indices.length > 1) {
                    indices.forEach((i) => {
                        const ocControl = formArray.at(i).get('oc');
                        if (ocControl) {
                            const existingErrors = ocControl.errors || {};
                            ocControl.setErrors({ ...existingErrors, duplicate: true });
                        }
                    });
                }
            });

            return null;
        };
    }

    private nextOcValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!(control instanceof FormArray)) return null;

            const allOcs = control.controls.map((ctrl) => ctrl.get('oc')?.value).filter((v) => !!v);

            control.controls.forEach((ctrl) => {
                const nextOcControl = ctrl.get('nextOc');
                if (!nextOcControl) return;

                const value = nextOcControl.value as string;
                const parts = value
                    ?.split(',')
                    .map((s) => s.trim())
                    .filter((s) => s);

                const invalids = parts?.filter((val) => !allOcs.includes(val));

                const errors = { ...nextOcControl.errors };
                if (invalids?.length) {
                    nextOcControl.setErrors({ ...errors, invalidNextOc: true });
                } else {
                    if (errors['invalidNextOc']) {
                        delete errors['invalidNextOc'];
                        nextOcControl.setErrors(Object.keys(errors).length ? errors : null);
                    }
                }
            });

            return null;
        };
    }

    isDuplicateOC(value: string, index: number): boolean {
        if (!value) return false;

        const allValues = this.processFlow.controls.map((ctrl) => ctrl.get('oc')?.value);
        return allValues.filter((v, i) => v === value && i !== index).length > 0;
    }

    get processFlow(): FormArray {
        return this.formGroup.get('listProcessFlow') as FormArray;
    }

    ngOnDestroy(): void {
        console.log('🧹 [ProcessFlowComponent] Unmounted');
    }

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    addItem() {
        let newItem = null;
        newItem = new FormGroupCustom(this.fb, {
            id: [null],
            processFlowId: [null],
            step: [null],
            oc: [null, [Validators.required, Validators.maxLength(4), Validators.pattern('^[A-Z0-9]{1,4}$')]],
            nextOc: [null],
            symbol: [null, Validators.required],
            operationDescription: [null, [Validators.required, Validators.maxLength(100)]],
            productAndProcessCharacteristics: [null, Validators.required],
            online: [null],
            offline: [null],
            action: [1],
        });
        this.processFlow.push(newItem);
        // this.processFlow.updateValueAndValidity();
    }

    removeItem(index: number) {
        // this.processFlow.removeAt(index);
        if (this.processFlow.at(index).get('processFlowId')?.value === null) {
            this.processFlow.removeAt(index);
        } else {
            this.processFlow.at(index).get('action')?.setValue(3);
            console.log(this.processFlow);
        }
    }

    sortProcessFlow() {
        if (this.instructionId === '0') return;
        this.processFlowService.getProcessFlow(this.instructionId).subscribe({
            next: (res) => {
                let processFlowDetails = [];

                this.detailProcessFlow = res;

                processFlowDetails = (res as unknown as { processFlows: ProcessFlowDetail[] }).processFlows || [];

                const newItems = processFlowDetails.map(
                    (item, index) =>
                        new FormGroupCustom(this.fb, {
                            id: [item.id],
                            processFlowId: [item.instructionId],
                            step: index + 1,
                            oc: [item.oc, [Validators.required, Validators.maxLength(4), Validators.pattern('^[A-Z0-9]{1,4}$')]],
                            nextOc: [item.nextOc],
                            symbol: [Number(item.symbol), Validators.required],
                            operationDescription: [item.description, [Validators.required, Validators.maxLength(100)]],
                            productAndProcessCharacteristics: [Number(item.characteristics), Validators.required],
                            online: [item.isOnline === 1 ? true : false],
                            offline: [item.isOnline === 1 ? false : true],
                            action: [item.instructionId ? 2 : 1],
                        }),
                );

                this.processFlow.clear();

                newItems.forEach((item) => this.processFlow.push(item));

                this.formGroup.patchValue({
                    reviewerIds: this.detailProcessFlow?.instructionInfo?.reviewers.map((item: any) => item.id),
                });
            },
            error: () => {},
        });
    }

    isValidFormat(data: any[]): boolean {
        if (!data.length) return false;
        const actualColumns = Object.keys(data[0]).map((col) => col.trim());
        console.log('actualColumns', actualColumns);
        return this.requiredColumns.every((col) => actualColumns.includes(col.trim()));
    }

    openTrackDialog(tab: number): void {
        this.showTrackDialog = true;
        this.selectedTab = tab;
    }

    handleCloseTrackDialog(): void {
        this.showTrackDialog = false;
    }

    onFileChange(event: any) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e: any) => {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });

            let validData: any[] | null = null;

            for (const sheetName of workbook.SheetNames) {
                const sheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(sheet, { defval: '' });

                if (this.isValidFormat(jsonData)) {
                    validData = jsonData;
                    break;
                }
            }

            if (!validData) {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'error',
                    summary: 'Lỗi',
                    detail: 'File import không đúng mẫu, vui lòng kiểm tra lại',
                });
                return;
            }

            console.log('✅ File hợp lệ:', validData);
            // Xử lý validData ở đây
            this.processFlow.clear();
            validData.forEach((item) => {
                this.processFlow.push(
                    new FormGroupCustom(this.fb, {
                        id: [null],
                        processFlowId: [null],
                        step: [item.STEP],
                        oc: [item['Operation Code (OC) *'].trim(), [Validators.required, Validators.maxLength(4), Validators.pattern('^[A-Z0-9]{1,4}$')]],
                        nextOc: [item['Next Operation Code (Next OC) *'].toString()],
                        symbol: [item['Đặc tính công việc (Symbol) *\n(điền các giá trị từ 1 - 6 theo Bảng mã)'], Validators.required],
                        operationDescription: [item['OPERATION DESCRIPTION (OD)'], [Validators.required, Validators.maxLength(100)]],
                        productAndProcessCharacteristics: [
                            item['Product and process characteristics *\n(điền 1 nếu là Man, 2 nếu là Machine, 3 nếu là Man/ Machine)'],
                            Validators.required,
                        ],
                        online: [item['Online/ Offline *\n(điền 1 nếu là Online, 2 nếu là Offline)'] === 1 ? true : false],
                        offline: [item['Online/ Offline *\n(điền 1 nếu là Online, 2 nếu là Offline)'] === 2 ? true : false],
                        action: [1],
                    }),
                );
            });
        };

        reader.readAsArrayBuffer(file);
    }

    onOcChanged(): void {
        // Hàm trống vẫn khiến Angular chạy lại change detection và update template
    }

    onSubmit(value: ProcessFlow): void {
        console.log(value, value.listProcessFlow);
    }

    getData() {
        return this.formGroup.getRawValue();
    }

    validateProcess(): boolean {
        const formArray = this.processFlow;

        formArray.controls.forEach((groupControl) => {
            const group = groupControl as FormGroup;
            group.markAllAsTouched();

            Object.keys(group.controls).forEach((key) => {
                const control = group.get(key);
                control?.updateValueAndValidity({ onlySelf: true, emitEvent: false });
            });
        });

        formArray.updateValueAndValidity();

        return this.formGroup.valid;
    }

    errorProcess() {
        // console.log('errorProcess');
    }

    exportProcessFlow() {
        this.processFlowService.exportProcessFlow(this.instructionId).subscribe({
            next: (res) => {
                const url = `${this.downloadBaseUrl}/${res}`;

                this.http.get(url, { responseType: 'blob' }).subscribe((blob) => {
                    const objectUrl = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = objectUrl;
                    a.download = 'process_flow.xlsx';
                    a.click();
                    URL.revokeObjectURL(objectUrl);
                });
            },
            error: () => {},
        });
    }

    handleApproverChange(event: any) {
        this.formGroup.patchValue({
            reviewerIds: event.map((item: any) => item.id),
        });
    }

    handlePreview() {
        const payload = {
            tabType: 1,
        };
        this.processFlowService.previewProcessFlow(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Gửi preview thành công',
                });
                this.sortProcessFlow();
            },
            error: () => {},
        });
    }

    handleComplete() {
        const payload = {
            tabType: 1,
            approvalStatus: 8,
        };
        this.processFlowService.comfirmProcessFlow(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Phê duyệt thành công',
                });
                this.sortProcessFlow();
            },
            error: () => {},
        });
    }

    handleReject() {
        const payload = {
            tabType: 1,
            approvalStatus: 4,
        };
        this.processFlowService.comfirmProcessFlow(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Từ chối thành công',
                });
                this.sortProcessFlow();
            },
            error: () => {},
        });
    }

    handleSubmit(value: ProcessFlow): void {
        this.disableAutoErrorMessage = true;
        if (!this.validateProcess()) return;
        const invalidItem = this.processFlow.controls.find((group) => {
            const online = group.get('online')?.value;
            const offline = group.get('offline')?.value;

            return (online && offline) || (!online && !offline);
        });
        if (invalidItem) {
            return;
        }

        // console.log(this.formGroup.getRawValue());
        // console.log(value, value.listProcessFlow);
        // console.log(this.processFlow.controls);

        this.formGroup.patchValue({
            versionId: this.productVersionId,
            phase: this.phase,
        });

        const payloadData = {
            processDetails: this.processFlow.controls.map((group) => {
                const online = group.get('online')?.value;
                return {
                    id: group.get('id')?.value,
                    oc: group.get('oc')?.value,
                    nextOc: group.get('nextOc')?.value,
                    symbol: group.get('symbol')?.value,
                    description: group.get('operationDescription')?.value,
                    characteristics: group.get('productAndProcessCharacteristics')?.value,
                    isOnline: online ? 1 : 0,
                    instructionInfoId: group.get('processFlowId')?.value,
                    action: group.get('action')?.value,
                };
            }),
            versionId: this.formGroup.get('versionId')?.value,
            phase: this.formGroup.get('phase')?.value,
            reviewerIds: this.formGroup.get('reviewerIds')?.value,
        };

        this.processFlowService.create(payloadData, this.instructionId).subscribe({
            next: (res) => {
                if (this.instructionId !== '0') {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật Process Flow thành công',
                    });
                } else {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo Process Flow thành công',
                    });
                }
                if (this.instructionId === '0') {
                    this.instructionId = res.id;
                    this.idProductInstruction.emit(res.id);
                    this.submitted.emit(res);
                }

                this.sortProcessFlow();
            },
            error: () => {},
        });
    }
}
