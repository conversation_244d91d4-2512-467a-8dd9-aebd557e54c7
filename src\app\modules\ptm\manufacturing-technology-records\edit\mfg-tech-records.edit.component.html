<app-sub-header [items]="itemsHeader" [action]="actionHeader"> </app-sub-header>
<ng-template #actionHeader>
    <app-wizard [currentState]="activeIndex" [states]="itemsStep"></app-wizard>
</ng-template>
<div style="padding: 1rem 1rem 0 1rem">
    <!-- product-info -->
    <app-product-info
        class="tw-mb-[2rem]"
        [fieldsLeft]="[
            { label: 'Dòng sản phẩm', value: 'lineName' },
            { label: 'Tên sản phẩm', value: 'name' },
            { label: 'Mô tả', value: 'description' },
            { label: 'VNPT P/N', value: 'vnptManPn' },
            { label: 'HW version', value: 'hardwareVersion' },
            { label: 'FW version', value: 'firmwareVersion' },
        ]"
        [fieldsRight]="[
            { label: 'Tên thương mại', value: 'tradeName' },
            { label: 'M<PERSON> thương mại', value: 'tradeCode' },
            { label: 'Model', value: 'modelName' },
            { label: 'Generation', value: 'generation' },
            { label: 'Hình ảnh sản phẩm', value: 'imageName', type: 'link', url: 'imageUrl' },
            { label: 'Giai đoạn', value: 'stage', type: 'tag' },
        ]"
        [product]="currentProduct"
        header="Thông tin sản phẩm"
    ></app-product-info>
    <!-- Design Document -->
    <ng-container>
        <app-product-info
            header="Tư liệu thiết kế"
            [fieldsLeft]="[
                {
                    label: 'Version HSSP',
                    value: 'id',
                    type: 'select',
                    editable: false,
                    options: designMaterials.listVersion,
                },
                { label: 'Giai đoạn HSSP', value: 'lifecycleStageText', type: 'tag' },
                { label: 'Trạng thái HSSP', value: 'statusText' },
                { label: 'Link tư liệu thiết kế', value: 'imageName', type: 'link', url: 'imageUrl' },
            ]"
            [product]="designMaterials.productRecordSelected"
            [forceShow]="designMaterials.listVersion?.length > 0"
            [emptyMessage]="'Vui lòng chọn tư liệu thiết kế để tiếp tục'"
            (fieldChanged)="onFieldChanged($event)"
        ></app-product-info>
    </ng-container>

    <app-form [formGroup]="filterForm" (onSubmit)="onSearch($event, 7)" layout="vertical" *ngIf="this.mode === 'create' && !isNotClone">
        <div class="tw-grid tw-grid-cols-12 tw-my-4">
            <div class="tw-col-span-10 filter-container tw-flex tw-items-center tw-flex-wrap mr-2">
                <span class="tw-font-bold">Nhân bản từ HSSP khác</span>
            </div>
            <div class="tw-col-span-2 tw-flex gap-4">
                <p-button [disabled]="isSearching" [loading]="isSearching" type="submit" label="Áp dụng" />
            </div>
        </div>
    </app-form>

    <p-panel header="Tài liệu sản xuất - Ver 1.0" styleClass="product-info-panel">
        <ng-template *ngIf="this.mode === 'create'" pTemplate="icons">
            <span>Chọn giai đoạn hồ sơ: </span>
            <p-dropdown [options]="optionsLifecycleStage" [(ngModel)]="selectedLifecycleStage" optionLabel="label" />
        </ng-template>
        <app-tab-view *ngIf="tabs.length" [tabs]="tabs" [(activeIndex)]="currentTab"> </app-tab-view>
    </p-panel>
</div>
