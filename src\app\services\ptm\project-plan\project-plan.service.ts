import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CreateProject, UpdateProject, ProjectPlan, SendApprovalRequest, ApprovalRequest } from 'src/app/models/interface/ptm/project-plan';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
// import { CreateProject } from 'src/app/models/interface/ptm';

@Injectable({ providedIn: 'root' })
export class ProjectPlanService {
    path = '/pr/api/project';
    #http = inject(HttpClient);

    createProjectPlan(payload: CreateProject): Observable<CreateProject> {
        return this.#http.post<CreateProject>(this.path, payload);
    }

    updateProjectPlan(id: number, payload: UpdateProject): Observable<UpdateProject> {
        return this.#http.put<UpdateProject>(`${this.path}/${id}`, payload);
    }

    getProject(id: number) {
        return this.#http.get(`${this.path}/${id}`);
    }

    updateWorkingSetting(projectId: number, payload: any): Observable<any> {
        return this.#http.put(`${this.path}/${projectId}/working-setting`, payload);
    }

    sendApprovalRequest(payload: SendApprovalRequest): Observable<any> {
        return this.#http.post<any>(`/pr/api/approval/project`, payload);
    }

    confirmApprovalRequest(payload: ApprovalRequest): Observable<any> {
        return this.#http.post<any>(`/pr/api/approval/project/confirm`, payload);
    }

    getInfoApprovalProject(id: number): Observable<any> {
        return this.#http.get<any>(`/pr/api/approval/project/${id}`);
    }

    deleteProject(id: number): Observable<any> {
        return this.#http.delete<any>(`/pr/api/project/${id}`);
    }

    updateProjectWorkingSettings(projectId: number, settings: any[]): Observable<any> {
        return this.#http.put(`${this.path}/${projectId}/working-setting`, {
            settings: settings,
        });
    }

    getProjectHistory(id: number) {
        return this.#http.get(`/pr/api/history/project/${id}`);
    }
    searchProjects(params: ParamsTable, body) {
        const customQs = this.buildCustomQueryString(params.native, params.pageable);
        const subQuery = [];
        if (body.projectName) {
            subQuery.push(`projectName=${encodeURIComponent(body.projectName)}`);
        }
        if (body.productName) {
            subQuery.push(`productName=${encodeURIComponent(body.productName)}`);
        }
        if (body.vnptManPn) {
            subQuery.push(`vnptManPn=${encodeURIComponent(body.vnptManPn)}`);
        }
        // if (body.startDate) {
        //     subQuery.push(`timelineFrom=${encodeURIComponent(body.startDate)}`);
        // }
        if (body.startDate && Array.isArray(body.startDate)) {
            const [from, to] = body.startDate;
            if (from) {
                subQuery.push(`timelineFrom=${new Date(from).getTime()}`);
            }
            if (to) {
                subQuery.push(`timelineTo=${new Date(to).getTime()}`);
            }
        }
        if (body.rate) {
            subQuery.push(`completeRatio=${encodeURIComponent(body.rate)}`);
        }
        if (body.milestoneMultilevelNumbering) {
            subQuery.push(`milestone=${encodeURIComponent(body.milestoneMultilevelNumbering)}`);
        }
        if (body.approvalStatus) {
            subQuery.push(`approvalStatus=${encodeURIComponent(body.approvalStatus)}`);
        }

        // Nếu customQs rỗng, thì URL là "/.../filter?".strip("&")
        const queryString = customQs.replace(/^&/, '');

        let query = '';
        if (subQuery.length) {
            query = `&${subQuery.join('&')}`;
        }

        return this.#http.get<ProjectPlan[]>(this.path + `/simple-search?${queryString}${query}`, {
            observe: 'response',
        });
    }
    // searchProjects(payload: any): Observable<any> {
    //     return this.#http.post<any>(`${this.path}/search`, payload, {
    //         observe: 'response',
    //     });
    // }
    private buildCustomQueryString(nativePart: string, pageablePart: string): string {
        // Bỏ dấu '&' đầu (nếu có)
        const rawNative = nativePart ? nativePart.replace(/^&/, '') : '';
        const rawPageable = pageablePart ? pageablePart.replace(/^&/, '') : '';

        // Tách rawNative thành từng cặp "key=value"
        const nativePairs = rawNative ? rawNative.split('&') : [];
        const multiParts: string[] = [];

        nativePairs.forEach((pair) => {
            const [key, value] = pair.split('=');
            if (key === 'productIds' && value) {
                // Nếu value = "2,3" → phân tách thành ["2","3"]
                value.split(',').forEach((id) => {
                    if (id) {
                        multiParts.push(`productIds=${encodeURIComponent(id)}`);
                    }
                });
            } else if (pair) {
                // Giữ nguyên nếu key khác
                multiParts.push(pair);
            }
        });

        // Tách pageable thành ["page=0","size=10", ...]
        const pageablePairs = rawPageable ? rawPageable.split('&') : [];

        // Ghép pageable trước, rồi native đã tách
        const allParts = [...pageablePairs, ...multiParts];
        return allParts.join('&');
    }
}
