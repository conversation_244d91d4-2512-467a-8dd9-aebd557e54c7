export interface CLDocViewModel {
    id: number;
    docType: string;
    formName: string;
    versionName: string | null;
    qualityControlDocumentId: number | null;
    status: number;
    statusText?: string;
    lastUpdate: number | null;
    lastUpdateText?: string;
    updatedByEmail: string;
    action: 0 | 1 | 2 | 3;
}
export interface CLDocRequest {
    id: number;
    docType: string;
    qualityControlDocumentId: number | null;
    action: 0 | 1 | 2 | 3;
}
export interface CLDocsSaveData {
    productInstructionId: string | number;
    payload: UpdateCLDocsPayload;
}
export interface UpdateCLDocsPayload {
    productVersionId: number;
    phase: number;
    qualityDocuments: CLDocRequest[];
}

export interface CLDocDto {
    id: number;
    docType: string;
    versionName: string;
    formName: string;
    status: number;
    updated: number;
    updatedBy: string;
    action?: number;
    qualityControlDocumentId: number | null;
}

export interface GetCLDocumentsResponse {
    qualityDocumentDtos: CLDocDto[];
    instructionInfoDto: InstructionInfoDto;
}
export interface InstructionInfoDto {
    instructionId: number;
}
