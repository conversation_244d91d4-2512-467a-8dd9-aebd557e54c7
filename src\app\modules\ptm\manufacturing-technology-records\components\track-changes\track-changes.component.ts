import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'app-track-changes',
    templateUrl: './track-changes.component.html',
    styleUrls: ['./track-changes.component.scss'],
    standalone: true,
    imports: [DialogModule, TableModule, CommonModule, ButtonModule],
})
export class TrackChangesComponent implements OnInit {
    @Input() visible = false;
    @Input() recordId!: number;
    @Output() closed = new EventEmitter<void>();

    trackChanges: {
        action: string;
        detail: string;
        user: string;
        time: string;
    }[] = [];

    ngOnInit(): void {
        console.log(this.recordId);

            this.fetchTrackChanges(this.recordId);

    }

    fetchTrackChanges(id: number): void {
        // 🔁 <PERSON><PERSON><PERSON> lập API
        this.trackChanges = [
            {
                action: 'Chỉnh sửa',
                detail: 'Thêm OC P120',
                user: 'pts.peo',
                time: '10:00am 03/04/2025',
            },
            {
                action: 'Chỉnh sửa',
                detail: 'Sửa OC150',
                user: 'pts.peo',
                time: '10:00am 03/04/2025',
            },
        ];
    }

    handleClose(): void {
        this.visible = false;
        this.closed.emit();
    }
}
