<div class="card-content">
    <label class="label" for="pf">Thông tin chung</label>
    <div class="tw-font-semibold">Tên {{ title }}: {{ name }}</div>
    <div class="tw-font-semibold tw-truncate tw-max-w-[300px]" tooltipPosition="top">
        Ngày tạo: {{ detailInfo?.instructionInfo?.createdAt | date: 'dd/MM/yyyy' }}
    </div>
    <div class="tw-font-semibold tw-truncate tw-max-w-[300px]" tooltipPosition="top">Ng<PERSON><PERSON><PERSON> tạo: {{ detailInfo?.instructionInfo?.creator?.fullName }}</div>
    <div class="tw-font-semibold">Trạng thái: {{ STATUS_MAP[detailInfo?.instructionInfo?.status] ?? 'Draft' }}</div>

    <div class="tw-grid tw-grid-cols-2 tw-gap-4">
        <app-form #form [formGroup]="formGroup">
            <app-custom-form-item [control]="" [label]="getInfo()">
                <app-combobox-nonRSQL
                    #userApproval
                    [fetchOnInit]="true"
                    type="select"
                    formControlName="reviewers"
                    fieldValue="id"
                    fieldLabel="email"
                    url="/auth/api/users/simple-search"
                    param="query"
                    [additionalParams]="{ page: 0, size: 100 }"
                    (onChange)="changeApprover($event)"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>
        </app-form>
    </div>
</div>
