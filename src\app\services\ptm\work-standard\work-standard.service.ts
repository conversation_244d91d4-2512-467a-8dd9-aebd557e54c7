import { inject, Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Pfmea, PfmeaUpdate } from '../../../models/interface/ptm/pfmea';

@Injectable({
    providedIn: 'root',
})
export class WorkStandardService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    getWorkStandard(id: string): Observable<any> {
        const url = `${this.path}/${id}/work-standard`;
        return this.#http.get<any>(url);
    }

    updateWorkStandard(id, data: any) {
        const url = `${this.path}/${id}/work-standard`;
        return this.#http.post<any>(url, data).pipe(catchError(this.handleError));
    }

    private handleError(error: HttpErrorResponse) {
        // có thể tuỳ chỉnh thông báo, logging, …
        console.error('API error', error);
        return throwError(() => error);
    }
}
