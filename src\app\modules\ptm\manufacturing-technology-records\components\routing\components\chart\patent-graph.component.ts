import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import * as d3 from 'd3';

@Component({
    selector: 'app-patent-graph',
    standalone: true,
    templateUrl: './patent-graph.component.html',
    styleUrls: ['./patent-graph.component.scss'],
})
export class PatentGraphComponent implements OnInit {
    @ViewChild('chart', { static: true }) chartRef!: ElementRef;
    @Input() nodes: any[] = [];
    @Input() links: any[] = [];

    ngOnInit() {
        if (this.nodes.length && this.links.length) {
            this.createGraph();
        }
    }

    ngOnChanges() {
        if (this.nodes.length && this.links.length) {
            this.createGraph();
        }
    }

    private createGraph(): void {
        const width = 1000;
        const height = 350;
        const element = this.chartRef.nativeElement;

        d3.select(element).selectAll('*').remove();

        const svg = d3.select(element).append('svg').attr('width', width).attr('height', height);

        // Define arrow markers for the links
        svg.append('defs')
            .selectAll('marker')
            .data(['arrow'])
            .enter()
            .append('marker')
            .attr('id', (d) => d)
            .attr('viewBox', '0 -5 10 10')
            .attr('refX', 15)
            .attr('refY', 0)
            .attr('markerWidth', 6)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .append('path')
            .attr('d', 'M0,-5L10,0L0,5')
            .attr('fill', 'orange');

        const simulation = d3
            .forceSimulation(this.nodes)
            .force(
                'link',
                d3
                    .forceLink(this.links)
                    .id((d: any) => d.id)
                    .distance(120),
            )
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(width / 2, height / 2));

        // Use path for curved links with marker
        const link = svg
            .append('g')
            .attr('stroke-opacity', 0.6)
            .selectAll('path')
            .data(this.links)
            .enter()
            .append('path')
            .attr('stroke', 'orange')
            .attr('stroke-width', 2)
            .attr('fill', 'none')
            .attr('marker-end', 'url(#arrow)');

        const node = svg
            .append('g')
            .attr('stroke', '#fff')
            .attr('stroke-width', 1.5)
            .selectAll('circle')
            .data(this.nodes)
            .enter()
            .append('circle')
            .attr('r', 8)
            .attr('fill', '#333')
            .call(
                d3
                    .drag<any, any>()
                    .on('start', (event, d) => {
                        if (!event.active) simulation.alphaTarget(0.3).restart();
                        d.fx = d.x;
                        d.fy = d.y;
                    })
                    .on('drag', (event, d) => {
                        d.fx = event.x;
                        d.fy = event.y;
                    })
                    .on('end', (event, d) => {
                        if (!event.active) simulation.alphaTarget(0);
                        d.fx = null;
                        d.fy = null;
                    }),
            );

        const label = svg
            .append('g')
            .selectAll('text')
            .data(this.nodes)
            .enter()
            .append('text')
            .text((d) => d.id)
            .attr('font-size', 12)
            .attr('dx', 10)
            .attr('dy', '.35em');

        simulation.on('tick', () => {
            // Draw curved paths for links
            link.attr('d', (d: any) => {
                const dx = d.target.x - d.source.x;
                const dy = d.target.y - d.source.y;
                const dr = Math.sqrt(dx * dx + dy * dy) * 1.2; // curve radius
                return `M${d.source.x},${d.source.y}A${dr},${dr} 0 0,1 ${d.target.x},${d.target.y}`;
            });

            node.attr('cx', (d: any) => d.x).attr('cy', (d: any) => d.y);

            label.attr('x', (d: any) => d.x).attr('y', (d: any) => d.y);
        });
    }
}
