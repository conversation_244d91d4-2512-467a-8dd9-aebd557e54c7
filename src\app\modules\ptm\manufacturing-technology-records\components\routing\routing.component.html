<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share
            [title]="title"
            [name]="name"
            [detailInfo]="detailRouting"
            [name]="name"
            (changeValueApprover)="handleApproverChange($event)"
        ></app-info-share>
    </div>

    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[10px]">
        <label class="label tw-w-[15rem]" for="pf">Chi tiết Routing</label>
        <div class="tw-flex tw-gap-2">
            <button
                pButton
                type="button"
                label="Track Changes"
                class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"
                (click)="openTrackDialog(2)"
            ></button>

            <button (click)="createAutoRouting()" pButton type="button" label="Tạo tự động" class="p-button-sm tw-h-10 tw-whitespace-nowrap"></button>
            <button
                pButton
                type="button"
                label="Upload file"
                class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"
                (click)="fileInputRouting.click()"
            ></button>
            <input #fileInputRouting type="file" style="display: none" accept="image/*" class="qc-upload-input" (input)="onSelectFile($event, 1)" />
            <div class="tw-cursor-pointer" *ngIf="nameFile && formGroup.get('chartType')?.value === 1" (click)="downloadFile()">
                {{ nameFile }}
            </div>
            <div *ngIf="nameFile && formGroup.get('chartType')?.value === 1" class="">
                <i class="pi pi-times delete-icon tw-text-red-500 tw-cursor-pointer" (click)="clearFile()" title="Xóa file"></i>
            </div>
        </div>
    </div>

    <div class="p-fluid tw-space-y-2" *ngIf="formGroup.get('chartType')?.value === 0">
        <app-patent-graph [nodes]="nodes" [links]="links"></app-patent-graph>
    </div>

    <div class="p-fluid tw-space-y-2" *ngIf="formGroup.get('chartType')?.value === 1 && nameFile">
        <img class="tw-max-h-[500px] tw-max-w-[1000px] tw-object-contain" [src]="urlFile" alt="chart" />
    </div>

    <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
        <p-button
            *ngIf="
                detailRouting?.instructionInfo?.status === 1 ||
                detailRouting?.instructionInfo?.status === 4 ||
                this.instructionId === '0' ||
                !detailRouting?.instructionInfo?.status
            "
            label="Lưu"
            type="submit"
            [loading]="isSaving | async"
            (onClick)="handleSubmit($event)"
            loadingIcon="pi pi-spinner pi-spin"
        >
        </p-button>
        <button
            *ngIf="
                detailRouting?.instructionInfo?.status === 1 ||
                detailRouting?.instructionInfo?.status === 4 ||
                this.instructionId === '0' ||
                !detailRouting?.instructionInfo?.status
            "
            label="Gửi Preview"
            pButton
            type="button"
            [disabled]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            class="p-button-secondary"
            (click)="handlePreview()"
        ></button>
        <p-button
            *ngIf="
                detailRouting?.instructionInfo?.status !== 1 &&
                detailRouting?.instructionInfo?.status !== 4 &&
                this.instructionId !== '0' &&
                detailRouting?.instructionInfo?.status
            "
            label="Phê duyệt"
            type="button"
            [disabled]="isApproving | async"
            [loading]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            (click)="handleComplete()"
        >
        </p-button>
        <button
            *ngIf="
                detailRouting?.instructionInfo?.status !== 1 &&
                detailRouting?.instructionInfo?.status !== 4 &&
                this.instructionId !== '0' &&
                detailRouting?.instructionInfo?.status
            "
            label="Từ chối"
            pButton
            type="button"
            [disabled]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            class="p-button-danger"
            (click)="handleReject()"
        ></button>
    </div>
    <ng-container *ngIf="showTrackDialog">
        <app-track-changes [visible]="showTrackDialog" [recordId]="selectedTab" (closed)="handleCloseTrackDialog()"></app-track-changes>
    </ng-container>
</ng-container>
